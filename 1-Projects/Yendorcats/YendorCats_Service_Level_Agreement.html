<!doctype html>
<html>
    <head>
        <title>YendorCats_Service_Level_Agreement</title>
        <meta charset='utf-8'/>
        <style>
 .ͼ1.cm-focused {outline: 1px dotted #212121;}
.ͼ1 {position: relative !important; box-sizing: border-box; display: flex !important; flex-direction: column;}
.ͼ1 .cm-scroller {display: flex !important; align-items: flex-start !important; font-family: monospace; line-height: 1.4; height: 100%; overflow-x: auto; position: relative; z-index: 0; overflow-anchor: none;}
.ͼ1 .cm-content[contenteditable=true] {-webkit-user-modify: read-write-plaintext-only;}
.ͼ1 .cm-content {margin: 0; flex-grow: 2; flex-shrink: 0; display: block; white-space: pre; word-wrap: normal; box-sizing: border-box; min-height: 100%; padding: 4px 0; outline: none;}
.ͼ1 .cm-lineWrapping {white-space: pre-wrap; white-space: break-spaces; word-break: break-word; overflow-wrap: anywhere; flex-shrink: 1;}
.ͼ2 .cm-content {caret-color: black;}
.ͼ3 .cm-content {caret-color: white;}
.ͼ1 .cm-line {display: block; padding: 0 2px 0 6px;}
.ͼ1 .cm-layer > * {position: absolute;}
.ͼ1 .cm-layer {position: absolute; left: 0; top: 0; contain: size style;}
.ͼ2 .cm-selectionBackground {background: #d9d9d9;}
.ͼ3 .cm-selectionBackground {background: #222;}
.ͼ2.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground {background: #d7d4f0;}
.ͼ3.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground {background: #233;}
.ͼ1 .cm-cursorLayer {pointer-events: none;}
.ͼ1.cm-focused > .cm-scroller > .cm-cursorLayer {animation: steps(1) cm-blink 1.2s infinite;}
@keyframes cm-blink {50% {opacity: 0;}}
@keyframes cm-blink2 {50% {opacity: 0;}}
.ͼ1 .cm-cursor, .ͼ1 .cm-dropCursor {border-left: 1.2px solid black; margin-left: -0.6px; pointer-events: none;}
.ͼ1 .cm-cursor {display: none;}
.ͼ3 .cm-cursor {border-left-color: #ddd;}
.ͼ1 .cm-dropCursor {position: absolute;}
.ͼ1.cm-focused > .cm-scroller > .cm-cursorLayer .cm-cursor {display: block;}
.ͼ1 .cm-iso {unicode-bidi: isolate;}
.ͼ1 .cm-announced {position: fixed; top: -10000px;}
@media print {.ͼ1 .cm-announced {display: none;}}
.ͼ2 .cm-activeLine {background-color: #cceeff44;}
.ͼ3 .cm-activeLine {background-color: #99eeff33;}
.ͼ2 .cm-specialChar {color: red;}
.ͼ3 .cm-specialChar {color: #f78;}
.ͼ1 .cm-gutters {flex-shrink: 0; display: flex; height: 100%; box-sizing: border-box; inset-inline-start: 0; z-index: 200;}
.ͼ2 .cm-gutters {background-color: #f5f5f5; color: #6c6c6c; border-right: 1px solid #ddd;}
.ͼ3 .cm-gutters {background-color: #333338; color: #ccc;}
.ͼ1 .cm-gutter {display: flex !important; flex-direction: column; flex-shrink: 0; box-sizing: border-box; min-height: 100%; overflow: hidden;}
.ͼ1 .cm-gutterElement {box-sizing: border-box;}
.ͼ1 .cm-lineNumbers .cm-gutterElement {padding: 0 3px 0 5px; min-width: 20px; text-align: right; white-space: nowrap;}
.ͼ2 .cm-activeLineGutter {background-color: #e2f2ff;}
.ͼ3 .cm-activeLineGutter {background-color: #222227;}
.ͼ1 .cm-panels {box-sizing: border-box; position: sticky; left: 0; right: 0; z-index: 300;}
.ͼ2 .cm-panels {background-color: #f5f5f5; color: black;}
.ͼ2 .cm-panels-top {border-bottom: 1px solid #ddd;}
.ͼ2 .cm-panels-bottom {border-top: 1px solid #ddd;}
.ͼ3 .cm-panels {background-color: #333338; color: white;}
.ͼ1 .cm-tab {display: inline-block; overflow: hidden; vertical-align: bottom;}
.ͼ1 .cm-widgetBuffer {vertical-align: text-top; height: 1em; width: 0; display: inline;}
.ͼ1 .cm-placeholder {color: #888; display: inline-block; vertical-align: top;}
.ͼ1 .cm-highlightSpace {background-image: radial-gradient(circle at 50% 55%, #aaa 20%, transparent 5%); background-position: center;}
.ͼ1 .cm-highlightTab {background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>'); background-size: auto 100%; background-position: right 90%; background-repeat: no-repeat;}
.ͼ1 .cm-trailingSpace {background-color: #ff332255;}
.ͼ1 .cm-button {vertical-align: middle; color: inherit; font-size: 70%; padding: .2em 1em; border-radius: 1px;}
.ͼ2 .cm-button:active {background-image: linear-gradient(#b4b4b4, #d0d3d6);}
.ͼ2 .cm-button {background-image: linear-gradient(#eff1f5, #d9d9df); border: 1px solid #888;}
.ͼ3 .cm-button:active {background-image: linear-gradient(#111, #333);}
.ͼ3 .cm-button {background-image: linear-gradient(#393939, #111); border: 1px solid #888;}
.ͼ1 .cm-textfield {vertical-align: middle; color: inherit; font-size: 70%; border: 1px solid silver; padding: .2em .5em;}
.ͼ2 .cm-textfield {background-color: white;}
.ͼ3 .cm-textfield {border: 1px solid #555; background-color: inherit;}
.ͼ1 .cm-foldPlaceholder {background-color: #eee; border: 1px solid #ddd; color: #888; border-radius: .2em; margin: 0 1px; padding: 0 1px; cursor: pointer;}
.ͼ1 .cm-foldGutter span {padding: 0 1px; cursor: pointer;}
.ͼp .cm-vimMode .cm-cursorLayer:not(.cm-vimCursorLayer) {display: none;}
.ͼp .cm-vim-panel {padding: 0px 10px; font-family: monospace; min-height: 1.3em;}
.ͼp .cm-vim-panel input {background: transparent; border: none; outline: none;}
.ͼo .cm-vimMode .cm-line {caret-color: transparent !important;}
.ͼo .cm-fat-cursor {position: absolute; border: none; white-space: pre;}
.ͼo.cm-focused > .cm-scroller > .cm-cursorLayer > .cm-fat-cursor {background: var(--interactive-accent); color: var(--text-on-accent);}
.ͼo:not(.cm-focused) > .cm-scroller > .cm-cursorLayer > .cm-fat-cursor {color: transparent !important;}
 @keyframes loading {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.workspace-leaf-content[data-type="git-view"] .button-border {
    border: 2px solid var(--interactive-accent);
    border-radius: var(--radius-s);
}

.workspace-leaf-content[data-type="git-view"] .view-content {
    padding: 0;
}

.workspace-leaf-content[data-type="git-history-view"] .view-content {
    padding: 0;
}

.loading > svg {
    animation: 2s linear infinite loading;
    transform-origin: 50% 50%;
    display: inline-block;
}

.obsidian-git-center {
    margin: auto;
    text-align: center;
    width: 50%;
}

.obsidian-git-textarea {
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.obsidian-git-disabled {
    opacity: 0.5;
}

.obsidian-git-center-button {
    display: block;
    margin: 20px auto;
}

.tooltip.mod-left {
    overflow-wrap: break-word;
}

.tooltip.mod-right {
    overflow-wrap: break-word;
}
.git-tools {
    display: flex;
    margin-left: auto;
}
.git-tools .type {
    padding-left: var(--size-2-1);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 11px;
}

.git-tools .type[data-type="M"] {
    color: orange;
}
.git-tools .type[data-type="D"] {
    color: red;
}
.git-tools .buttons {
    display: flex;
}
.git-tools .buttons > * {
    padding: 0 0;
    height: auto;
}

.is-active .git-tools .buttons > * {
    color: var(--nav-item-color-active);
}

.git-author {
    color: var(--text-accent);
}

.git-date {
    color: var(--text-accent);
}

.git-ref {
    color: var(--text-accent);
}

.workspace-leaf-content[data-type="diff-view"] .d2h-d-none {
    display: none;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-wrapper {
    text-align: left;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-header {
    background-color: var(--background-primary);
    border-bottom: 1px solid var(--interactive-accent);
    font-family: var(--font-monospace);
    height: 35px;
    padding: 5px 10px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-header,
.workspace-leaf-content[data-type="diff-view"] .d2h-file-stats {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-stats {
    font-size: 14px;
    margin-left: auto;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-lines-added {
    border: 1px solid #b4e2b4;
    border-radius: 5px 0 0 5px;
    color: #399839;
    padding: 2px;
    text-align: right;
    vertical-align: middle;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-lines-deleted {
    border: 1px solid #e9aeae;
    border-radius: 0 5px 5px 0;
    color: #c33;
    margin-left: 1px;
    padding: 2px;
    text-align: left;
    vertical-align: middle;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-name-wrapper {
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    font-size: 15px;
    width: 100%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-name {
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-wrapper {
    border: 1px solid var(--background-modifier-border);
    border-radius: 3px;
    margin-bottom: 1em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-collapse {
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    border: 1px solid var(--background-modifier-border);
    border-radius: 3px;
    cursor: pointer;
    display: none;
    font-size: 12px;
    justify-content: flex-end;
    padding: 4px 8px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-collapse.d2h-selected {
    background-color: #c8e1ff;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-collapse-input {
    margin: 0 4px 0 0;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-diff-table {
    border-collapse: collapse;
    font-family: Menlo, Consolas, monospace;
    font-size: 13px;
    width: 100%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-files-diff {
    width: 100%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-diff {
    overflow-y: hidden;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-side-diff {
    display: inline-block;
    margin-bottom: -8px;
    margin-right: -4px;
    overflow-x: scroll;
    overflow-y: hidden;
    width: 50%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line {
    padding: 0 8em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line {
    display: inline-block;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    white-space: nowrap;
    width: 100%;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line {
    padding: 0 4.5em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line-ctn {
    word-wrap: normal;
    background: none;
    display: inline-block;
    padding: 0;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
    vertical-align: middle;
    white-space: pre;
    width: 100%;
}

.theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-code-line del,
.theme-light
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-code-side-line
    del {
    background-color: #ffb6ba;
}

.theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-code-line del,
.theme-dark
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-code-side-line
    del {
    background-color: #8d232881;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line del,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-line ins,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line del,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-line ins {
    border-radius: 0.2em;
    display: inline-block;
    margin-top: -1px;
    text-decoration: none;
    vertical-align: middle;
}

.theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-code-line ins,
.theme-light
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-code-side-line
    ins {
    background-color: #97f295;
    text-align: left;
}

.theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-code-line ins,
.theme-dark
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-code-side-line
    ins {
    background-color: #1d921996;
    text-align: left;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line-prefix {
    word-wrap: normal;
    background: none;
    display: inline;
    padding: 0;
    white-space: pre;
}

.workspace-leaf-content[data-type="diff-view"] .line-num1 {
    float: left;
}

.workspace-leaf-content[data-type="diff-view"] .line-num1,
.workspace-leaf-content[data-type="diff-view"] .line-num2 {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    overflow: hidden;
    padding: 0 0.5em;
    text-overflow: ellipsis;
    width: 3.5em;
}

.workspace-leaf-content[data-type="diff-view"] .line-num2 {
    float: right;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber {
    background-color: var(--background-primary);
    border: solid var(--background-modifier-border);
    border-width: 0 1px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: var(--text-muted);
    cursor: pointer;
    display: inline-block;
    position: absolute;
    text-align: right;
    width: 7.5em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber:after {
    content: "\200b";
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber {
    background-color: var(--background-primary);
    border: solid var(--background-modifier-border);
    border-width: 0 1px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    color: var(--text-muted);
    cursor: pointer;
    display: inline-block;
    overflow: hidden;
    padding: 0 0.5em;
    position: absolute;
    text-align: right;
    text-overflow: ellipsis;
    width: 4em;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-diff-tbody tr {
    position: relative;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber:after {
    content: "\200b";
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-emptyplaceholder,
.workspace-leaf-content[data-type="diff-view"] .d2h-emptyplaceholder {
    background-color: var(--background-primary);
    border-color: var(--background-modifier-border);
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-line-prefix,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber,
.workspace-leaf-content[data-type="diff-view"] .d2h-emptyplaceholder {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-code-linenumber,
.workspace-leaf-content[data-type="diff-view"] .d2h-code-side-linenumber {
    direction: rtl;
}

.theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-del {
    background-color: #fee8e9;
    border-color: #e9aeae;
}

.theme-light .workspace-leaf-content[data-type="diff-view"] .d2h-ins {
    background-color: #dfd;
    border-color: #b4e2b4;
}

.theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-del {
    background-color: #521b1d83;
    border-color: #691d1d73;
}

.theme-dark .workspace-leaf-content[data-type="diff-view"] .d2h-ins {
    background-color: rgba(30, 71, 30, 0.5);
    border-color: #13501381;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-info {
    background-color: var(--background-primary);
    border-color: var(--background-modifier-border);
    color: var(--text-normal);
}

.theme-light
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-file-diff
    .d2h-del.d2h-change {
    background-color: #fdf2d0;
}

.theme-dark
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-file-diff
    .d2h-del.d2h-change {
    background-color: #55492480;
}

.theme-light
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-file-diff
    .d2h-ins.d2h-change {
    background-color: #ded;
}

.theme-dark
    .workspace-leaf-content[data-type="diff-view"]
    .d2h-file-diff
    .d2h-ins.d2h-change {
    background-color: rgba(37, 78, 37, 0.418);
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-wrapper {
    margin-bottom: 10px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-wrapper a {
    color: #3572b0;
    text-decoration: none;
}

.workspace-leaf-content[data-type="diff-view"]
    .d2h-file-list-wrapper
    a:visited {
    color: #3572b0;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-header {
    text-align: left;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-title {
    font-weight: 700;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list-line {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    text-align: left;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list {
    display: block;
    list-style: none;
    margin: 0;
    padding: 0;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list > li {
    border-bottom: 1px solid var(--background-modifier-border);
    margin: 0;
    padding: 5px 10px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-list > li:last-child {
    border-bottom: none;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-file-switch {
    cursor: pointer;
    display: none;
    font-size: 10px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-icon {
    fill: currentColor;
    margin-right: 10px;
    vertical-align: middle;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-deleted {
    color: #c33;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-added {
    color: #399839;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-changed {
    color: #d0b44c;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-moved {
    color: #3572b0;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-tag {
    background-color: var(--background-primary);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    font-size: 10px;
    margin-left: 5px;
    padding: 0 2px;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-deleted-tag {
    border: 2px solid #c33;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-added-tag {
    border: 1px solid #399839;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-changed-tag {
    border: 1px solid #d0b44c;
}

.workspace-leaf-content[data-type="diff-view"] .d2h-moved-tag {
    border: 1px solid #3572b0;
}

/* ====================== Line Authoring Information ====================== */

.cm-gutterElement.obs-git-blame-gutter {
    /* Add background color to spacing inbetween and around the gutter for better aesthetics */
    border-width: 0px 2px 0.2px 2px;
    border-style: solid;
    border-color: var(--background-secondary);
    background-color: var(--background-secondary);
}

.cm-gutterElement.obs-git-blame-gutter > div,
.line-author-settings-preview {
    /* delegate text color to settings */
    color: var(--obs-git-gutter-text);
    font-family: monospace;
    height: 100%; /* ensure, that age-based background color occupies entire parent */
    text-align: right;
    padding: 0px 6px 0px 6px;
    white-space: pre; /* Keep spaces and do not collapse them. */
}

@media (max-width: 800px) {
    /* hide git blame gutter not to superpose text */
    .cm-gutterElement.obs-git-blame-gutter {
        display: none;
    }
}

.git-unified-diff-view,
.git-split-diff-view .cm-deletedLine .cm-changedText {
    background-color: #ee443330;
}

.git-unified-diff-view,
.git-split-diff-view .cm-insertedLine .cm-changedText {
    background-color: #22bb2230;
}

/* Limits the scrollbar to the view body */
.git-view {
    display: flex;
    flex-direction: column;
    position: relative;
    height: 100%;
}

.git-obscure-prompt[git-is-obscured="true"] #git-show-password:after {
    -webkit-mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svg-icon lucide-eye"><path d="M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0"></path><circle cx="12" cy="12" r="3"></circle></svg>');
}

.git-obscure-prompt[git-is-obscured="false"] #git-show-password:after {
    -webkit-mask-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="svg-icon lucide-eye-off"><path d="M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49"></path><path d="M14.084 14.158a3 3 0 0 1-4.242-4.242"></path><path d="M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143"></path><path d="m2 2 20 20"></path></svg>');
}

/* Override styling of Codemirror merge view "collapsed lines" indicator */
.git-split-diff-view .ͼ2 .cm-collapsedLines {
    background: var(--interactive-normal);
    border-radius: var(--radius-m);
    color: var(--text-accent);
    font-size: var(--font-small);
    padding: var(--size-4-1) var(--size-4-1);
}
.git-split-diff-view .ͼ2 .cm-collapsedLines:hover {
    background: var(--interactive-hover);
    color: var(--text-accent-hover);
}
 .block-language-dataview {
    overflow-y: auto;
}

/*****************/
/** Table Views **/
/*****************/

/* List View Default Styling; rendered internally as a table. */
.table-view-table {
    width: 100%;
}

.table-view-table > thead > tr, .table-view-table > tbody > tr {
    margin-top: 1em;
    margin-bottom: 1em;
    text-align: left;
}

.table-view-table > tbody > tr:hover {
    background-color: var(--table-row-background-hover);
}

.table-view-table > thead > tr > th {
    font-weight: 700;
    font-size: larger;
    border-top: none;
    border-left: none;
    border-right: none;
    border-bottom: solid;

    max-width: 100%;
}

.table-view-table > tbody > tr > td {
    text-align: left;
    border: none;
    font-weight: 400;
    max-width: 100%;
}

.table-view-table ul, .table-view-table ol {
    margin-block-start: 0.2em !important;
    margin-block-end: 0.2em !important;
}

/** Rendered value styling for any view. */
.dataview-result-list-root-ul {
    padding: 0em !important;
    margin: 0em !important;
}

.dataview-result-list-ul {
    margin-block-start: 0.2em !important;
    margin-block-end: 0.2em !important;
}

/** Generic grouping styling. */
.dataview.result-group {
    padding-left: 8px;
}

/*******************/
/** Inline Fields **/
/*******************/

.dataview.inline-field-key {
    padding-left: 8px;
    padding-right: 8px;
    font-family: var(--font-monospace);
    background-color: var(--background-primary-alt);
    color: var(--nav-item-color-selected);
}

.dataview.inline-field-value {
    padding-left: 8px;
    padding-right: 8px;
    font-family: var(--font-monospace);
    background-color: var(--background-secondary-alt);
    color: var(--nav-item-color-selected);
}

.dataview.inline-field-standalone-value {
    padding-left: 8px;
    padding-right: 8px;
    font-family: var(--font-monospace);
    background-color: var(--background-secondary-alt);
    color: var(--nav-item-color-selected);
}

/***************/
/** Task View **/
/***************/

.dataview.task-list-item, .dataview.task-list-basic-item {
    margin-top: 3px;
    margin-bottom: 3px;
    transition: 0.4s;
}

.dataview.task-list-item:hover, .dataview.task-list-basic-item:hover {
    background-color: var(--text-selection);
    box-shadow: -40px 0 0 var(--text-selection);
    cursor: pointer;
}

/*****************/
/** Error Views **/
/*****************/

div.dataview-error-box {
    width: 100%;
    min-height: 150px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 4px dashed var(--background-secondary);
}

.dataview-error-message {
    color: var(--text-muted);
    text-align: center;
}

/*************************/
/** Additional Metadata **/
/*************************/

.dataview.small-text {
    font-size: smaller;
    color: var(--text-muted);
    margin-left: 3px;
}

.dataview.small-text::before {
	content: "(";
}

.dataview.small-text::after {
	content: ")";
}
 .templater_search {
    width: calc(100% - 20px);
}

.templater_div {
    border-top: 1px solid var(--background-modifier-border);
}

.templater_div > .setting-item {
    border-top: none !important;
    align-self: center;
}

.templater_div > .setting-item > .setting-item-control {
    justify-content: space-around;
    padding: 0;
    width: 100%;
}

.templater_div
    > .setting-item
    > .setting-item-control
    > .setting-editor-extra-setting-button {
    align-self: center;
}

.templater_donating {
    margin: 10px;
}

.templater_title {
    margin: 0;
    padding: 0;
    margin-top: 5px;
    text-align: center;
}

.templater_template {
    align-self: center;
    margin-left: 5px;
    margin-right: 5px;
    width: 70%;
}

.templater_cmd {
    margin-left: 5px;
    margin-right: 5px;
    font-size: 14px;
    width: 100%;
}

.templater_div2 > .setting-item {
    align-content: center;
    justify-content: center;
}

.templater-prompt-div {
    display: flex;
}

.templater-prompt-form {
    display: flex;
    flex-grow: 1;
}

.templater-prompt-input {
    flex-grow: 1;
}

.templater-button-div {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 1rem;
}

textarea.templater-prompt-input {
    height: 10rem;
}

textarea.templater-prompt-input:focus {
    border-color: var(--interactive-accent);
}

.cm-s-obsidian .templater-command-bg {
    left: 0px;
    right: 0px;
    background-color: var(--background-primary-alt);
}

.cm-s-obsidian .cm-templater-command {
    font-size: 0.85em;
    font-family: var(--font-monospace);
    line-height: 1.3;
}

.cm-s-obsidian .templater-inline .cm-templater-command {
    background-color: var(--background-primary-alt);
}

.cm-s-obsidian .cm-templater-command.cm-templater-opening-tag {
    font-weight: bold;
}

.cm-s-obsidian .cm-templater-command.cm-templater-closing-tag {
    font-weight: bold;
}

.cm-s-obsidian .cm-templater-command.cm-templater-interpolation-tag {
    color: var(--code-property, #008bff);
}

.cm-s-obsidian .cm-templater-command.cm-templater-execution-tag {
    color: var(--code-function, #c0d700);
}

.cm-s-obsidian .cm-templater-command.cm-keyword {
    color: var(--code-keyword, #00a7aa);
    font-weight: normal;
}

.cm-s-obsidian .cm-templater-command.cm-atom {
    color: var(--code-normal, #f39b35);
}

.cm-s-obsidian .cm-templater-command.cm-value,
.cm-s-obsidian .cm-templater-command.cm-number,
.cm-s-obsidian .cm-templater-command.cm-type {
    color: var(--code-value, #a06fca);
}

.cm-s-obsidian .cm-templater-command.cm-def,
.cm-s-obsidian .cm-templater-command.cm-type.cm-def {
    color: var(--code-normal, var(--text-normal));
}

.cm-s-obsidian .cm-templater-command.cm-property,
.cm-s-obsidian .cm-templater-command.cm-property.cm-def,
.cm-s-obsidian .cm-templater-command.cm-attribute {
    color: var(--code-function, #98e342);
}

.cm-s-obsidian .cm-templater-command.cm-variable,
.cm-s-obsidian .cm-templater-command.cm-variable-2,
.cm-s-obsidian .cm-templater-command.cm-variable-3,
.cm-s-obsidian .cm-templater-command.cm-meta {
    color: var(--code-property, #d4d4d4);
}

.cm-s-obsidian .cm-templater-command.cm-callee,
.cm-s-obsidian .cm-templater-command.cm-operator,
.cm-s-obsidian .cm-templater-command.cm-qualifier,
.cm-s-obsidian .cm-templater-command.cm-builtin {
    color: var(--code-operator, #fc4384);
}

.cm-s-obsidian .cm-templater-command.cm-tag {
    color: var(--code-tag, #fc4384);
}

.cm-s-obsidian .cm-templater-command.cm-comment,
.cm-s-obsidian .cm-templater-command.cm-comment.cm-tag,
.cm-s-obsidian .cm-templater-command.cm-comment.cm-attribute {
    color: var(--code-comment, #696d70);
}

.cm-s-obsidian .cm-templater-command.cm-string,
.cm-s-obsidian .cm-templater-command.cm-string-2 {
    color: var(--code-string, #e6db74);
}

.cm-s-obsidian .cm-templater-command.cm-header,
.cm-s-obsidian .cm-templater-command.cm-hr {
    color: var(--code-keyword, #da7dae);
}

.cm-s-obsidian .cm-templater-command.cm-link {
    color: var(--code-normal, #696d70);
}

.cm-s-obsidian .cm-templater-command.cm-error {
    border-bottom: 1px solid #c42412;
}

.CodeMirror-hints {
    position: absolute;
    z-index: 10;
    overflow: hidden;
    list-style: none;

    margin: 0;
    padding: 2px;

    -webkit-box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.2);
    -moz-box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.2);
    box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    border: 1px solid silver;

    background: white;
    font-size: 90%;
    font-family: monospace;

    max-height: 20em;
    overflow-y: auto;
}

.CodeMirror-hint {
    margin: 0;
    padding: 0 4px;
    border-radius: 2px;
    white-space: pre;
    color: black;
    cursor: pointer;
}

li.CodeMirror-hint-active {
    background: #08f;
    color: white;
}
 @charset "UTF-8";.flatpickr-calendar{background:transparent;opacity:0;display:none;text-align:center;visibility:hidden;padding:0;-webkit-animation:none;animation:none;direction:ltr;border:0;font-size:14px;line-height:24px;border-radius:5px;position:absolute;width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;-ms-touch-action:manipulation;touch-action:manipulation;background:#fff;-webkit-box-shadow:1px 0 0 #e6e6e6,-1px 0 0 #e6e6e6,0 1px 0 #e6e6e6,0 -1px 0 #e6e6e6,0 3px 13px rgba(0,0,0,.08);box-shadow:1px 0 #e6e6e6,-1px 0 #e6e6e6,0 1px #e6e6e6,0 -1px #e6e6e6,0 3px 13px #00000014}.flatpickr-calendar.open,.flatpickr-calendar.inline{opacity:1;max-height:640px;visibility:visible}.flatpickr-calendar.open{display:inline-block;z-index:99999}.flatpickr-calendar.animate.open{-webkit-animation:fpFadeInDown .3s cubic-bezier(.23,1,.32,1);animation:fpFadeInDown .3s cubic-bezier(.23,1,.32,1)}.flatpickr-calendar.inline{display:block;position:relative;top:2px}.flatpickr-calendar.static{position:absolute;top:calc(100% + 2px)}.flatpickr-calendar.static.open{z-index:999;display:block}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+1) .flatpickr-day.inRange:nth-child(7n+7){-webkit-box-shadow:none!important;box-shadow:none!important}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+2) .flatpickr-day.inRange:nth-child(7n+1){-webkit-box-shadow:-2px 0 0 #e6e6e6,5px 0 0 #e6e6e6;box-shadow:-2px 0 #e6e6e6,5px 0 #e6e6e6}.flatpickr-calendar .hasWeeks .dayContainer,.flatpickr-calendar .hasTime .dayContainer{border-bottom:0;border-bottom-right-radius:0;border-bottom-left-radius:0}.flatpickr-calendar .hasWeeks .dayContainer{border-left:0}.flatpickr-calendar.hasTime .flatpickr-time{height:40px;border-top:1px solid #e6e6e6}.flatpickr-calendar.noCalendar.hasTime .flatpickr-time{height:auto}.flatpickr-calendar:before,.flatpickr-calendar:after{position:absolute;display:block;pointer-events:none;border:solid transparent;content:"";height:0;width:0;left:22px}.flatpickr-calendar.rightMost:before,.flatpickr-calendar.arrowRight:before,.flatpickr-calendar.rightMost:after,.flatpickr-calendar.arrowRight:after{left:auto;right:22px}.flatpickr-calendar.arrowCenter:before,.flatpickr-calendar.arrowCenter:after{left:50%;right:50%}.flatpickr-calendar:before{border-width:5px;margin:0 -5px}.flatpickr-calendar:after{border-width:4px;margin:0 -4px}.flatpickr-calendar.arrowTop:before,.flatpickr-calendar.arrowTop:after{bottom:100%}.flatpickr-calendar.arrowTop:before{border-bottom-color:#e6e6e6}.flatpickr-calendar.arrowTop:after{border-bottom-color:#fff}.flatpickr-calendar.arrowBottom:before,.flatpickr-calendar.arrowBottom:after{top:100%}.flatpickr-calendar.arrowBottom:before{border-top-color:#e6e6e6}.flatpickr-calendar.arrowBottom:after{border-top-color:#fff}.flatpickr-calendar:focus{outline:0}.flatpickr-wrapper{position:relative;display:inline-block}.flatpickr-months{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.flatpickr-months .flatpickr-month{background:transparent;color:#000000e6;fill:#000000e6;height:34px;line-height:1;text-align:center;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;overflow:hidden;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}.flatpickr-months .flatpickr-prev-month,.flatpickr-months .flatpickr-next-month{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;text-decoration:none;cursor:pointer;position:absolute;top:0;height:34px;padding:10px;z-index:3;color:#000000e6;fill:#000000e6}.flatpickr-months .flatpickr-prev-month.flatpickr-disabled,.flatpickr-months .flatpickr-next-month.flatpickr-disabled{display:none}.flatpickr-months .flatpickr-prev-month i,.flatpickr-months .flatpickr-next-month i{position:relative}.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,.flatpickr-months .flatpickr-next-month.flatpickr-prev-month{left:0}.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,.flatpickr-months .flatpickr-next-month.flatpickr-next-month{right:0}.flatpickr-months .flatpickr-prev-month:hover,.flatpickr-months .flatpickr-next-month:hover{color:#959ea9}.flatpickr-months .flatpickr-prev-month:hover svg,.flatpickr-months .flatpickr-next-month:hover svg{fill:#f64747}.flatpickr-months .flatpickr-prev-month svg,.flatpickr-months .flatpickr-next-month svg{width:14px;height:14px}.flatpickr-months .flatpickr-prev-month svg path,.flatpickr-months .flatpickr-next-month svg path{-webkit-transition:fill .1s;transition:fill .1s;fill:inherit}.numInputWrapper{position:relative;height:auto}.numInputWrapper input,.numInputWrapper span{display:inline-block}.numInputWrapper input{width:100%}.numInputWrapper input::-ms-clear{display:none}.numInputWrapper input::-webkit-outer-spin-button,.numInputWrapper input::-webkit-inner-spin-button{margin:0;-webkit-appearance:none}.numInputWrapper span{position:absolute;right:0;width:14px;padding:0 4px 0 2px;height:50%;line-height:50%;opacity:0;cursor:pointer;border:1px solid rgba(57,57,57,.15);-webkit-box-sizing:border-box;box-sizing:border-box}.numInputWrapper span:hover{background:rgba(0,0,0,.1)}.numInputWrapper span:active{background:rgba(0,0,0,.2)}.numInputWrapper span:after{display:block;content:"";position:absolute}.numInputWrapper span.arrowUp{top:0;border-bottom:0}.numInputWrapper span.arrowUp:after{border-left:4px solid transparent;border-right:4px solid transparent;border-bottom:4px solid rgba(57,57,57,.6);top:26%}.numInputWrapper span.arrowDown{top:50%}.numInputWrapper span.arrowDown:after{border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid rgba(57,57,57,.6);top:40%}.numInputWrapper span svg{width:inherit;height:auto}.numInputWrapper span svg path{fill:#00000080}.numInputWrapper:hover{background:rgba(0,0,0,.05)}.numInputWrapper:hover span{opacity:1}.flatpickr-current-month{font-size:135%;line-height:inherit;font-weight:300;color:inherit;position:absolute;width:75%;left:12.5%;padding:7.48px 0 0;line-height:1;height:34px;display:inline-block;text-align:center;-webkit-transform:translate3d(0px,0px,0px);transform:translateZ(0)}.flatpickr-current-month span.cur-month{font-family:inherit;font-weight:700;color:inherit;display:inline-block;margin-left:.5ch;padding:0}.flatpickr-current-month span.cur-month:hover{background:rgba(0,0,0,.05)}.flatpickr-current-month .numInputWrapper{width:6ch;width:7ch\fffd;display:inline-block}.flatpickr-current-month .numInputWrapper span.arrowUp:after{border-bottom-color:#000000e6}.flatpickr-current-month .numInputWrapper span.arrowDown:after{border-top-color:#000000e6}.flatpickr-current-month input.cur-year{background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;color:inherit;cursor:text;padding:0 0 0 .5ch;margin:0;display:inline-block;font-size:inherit;font-family:inherit;font-weight:300;line-height:inherit;height:auto;border:0;border-radius:0;vertical-align:initial;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield}.flatpickr-current-month input.cur-year:focus{outline:0}.flatpickr-current-month input.cur-year[disabled],.flatpickr-current-month input.cur-year[disabled]:hover{font-size:100%;color:#00000080;background:transparent;pointer-events:none}.flatpickr-current-month .flatpickr-monthDropdown-months{appearance:menulist;background:transparent;border:none;border-radius:0;box-sizing:border-box;color:inherit;cursor:pointer;font-size:inherit;font-family:inherit;font-weight:300;height:auto;line-height:inherit;margin:-1px 0 0;outline:none;padding:0 0 0 .5ch;position:relative;vertical-align:initial;-webkit-box-sizing:border-box;-webkit-appearance:menulist;-moz-appearance:menulist;width:auto}.flatpickr-current-month .flatpickr-monthDropdown-months:focus,.flatpickr-current-month .flatpickr-monthDropdown-months:active{outline:none}.flatpickr-current-month .flatpickr-monthDropdown-months:hover{background:rgba(0,0,0,.05)}.flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month{background-color:transparent;outline:none;padding:0}.flatpickr-weekdays{background:transparent;text-align:center;overflow:hidden;width:100%;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;height:28px}.flatpickr-weekdays .flatpickr-weekdaycontainer{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}span.flatpickr-weekday{cursor:default;font-size:90%;background:transparent;color:#0000008a;line-height:1;margin:0;text-align:center;display:block;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;font-weight:bolder}.dayContainer,.flatpickr-weeks{padding:1px 0 0}.flatpickr-days{position:relative;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-ms-flex-align:start;align-items:flex-start;width:307.875px}.flatpickr-days:focus{outline:0}.dayContainer{padding:0;outline:0;text-align:left;width:307.875px;min-width:307.875px;max-width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;display:inline-block;display:-ms-flexbox;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-wrap:wrap;-ms-flex-pack:justify;-webkit-justify-content:space-around;justify-content:space-around;-webkit-transform:translate3d(0px,0px,0px);transform:translateZ(0);opacity:1}.dayContainer+.dayContainer{-webkit-box-shadow:-1px 0 0 #e6e6e6;box-shadow:-1px 0 #e6e6e6}.flatpickr-day{background:none;border:1px solid transparent;border-radius:150px;-webkit-box-sizing:border-box;box-sizing:border-box;color:#393939;cursor:pointer;font-weight:400;width:14.2857143%;-webkit-flex-basis:14.2857143%;-ms-flex-preferred-size:14.2857143%;flex-basis:14.2857143%;max-width:39px;height:39px;line-height:39px;margin:0;display:inline-block;position:relative;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;text-align:center}.flatpickr-day.inRange,.flatpickr-day.prevMonthDay.inRange,.flatpickr-day.nextMonthDay.inRange,.flatpickr-day.today.inRange,.flatpickr-day.prevMonthDay.today.inRange,.flatpickr-day.nextMonthDay.today.inRange,.flatpickr-day:hover,.flatpickr-day.prevMonthDay:hover,.flatpickr-day.nextMonthDay:hover,.flatpickr-day:focus,.flatpickr-day.prevMonthDay:focus,.flatpickr-day.nextMonthDay:focus{cursor:pointer;outline:0;background:#e6e6e6;border-color:#e6e6e6}.flatpickr-day.today{border-color:#959ea9}.flatpickr-day.today:hover,.flatpickr-day.today:focus{border-color:#959ea9;background:#959ea9;color:#fff}.flatpickr-day.selected,.flatpickr-day.startRange,.flatpickr-day.endRange,.flatpickr-day.selected.inRange,.flatpickr-day.startRange.inRange,.flatpickr-day.endRange.inRange,.flatpickr-day.selected:focus,.flatpickr-day.startRange:focus,.flatpickr-day.endRange:focus,.flatpickr-day.selected:hover,.flatpickr-day.startRange:hover,.flatpickr-day.endRange:hover,.flatpickr-day.selected.prevMonthDay,.flatpickr-day.startRange.prevMonthDay,.flatpickr-day.endRange.prevMonthDay,.flatpickr-day.selected.nextMonthDay,.flatpickr-day.startRange.nextMonthDay,.flatpickr-day.endRange.nextMonthDay{background:#569ff7;-webkit-box-shadow:none;box-shadow:none;color:#fff;border-color:#569ff7}.flatpickr-day.selected.startRange,.flatpickr-day.startRange.startRange,.flatpickr-day.endRange.startRange{border-radius:50px 0 0 50px}.flatpickr-day.selected.endRange,.flatpickr-day.startRange.endRange,.flatpickr-day.endRange.endRange{border-radius:0 50px 50px 0}.flatpickr-day.selected.startRange+.endRange:not(:nth-child(7n+1)),.flatpickr-day.startRange.startRange+.endRange:not(:nth-child(7n+1)),.flatpickr-day.endRange.startRange+.endRange:not(:nth-child(7n+1)){-webkit-box-shadow:-10px 0 0 #569ff7;box-shadow:-10px 0 #569ff7}.flatpickr-day.selected.startRange.endRange,.flatpickr-day.startRange.startRange.endRange,.flatpickr-day.endRange.startRange.endRange{border-radius:50px}.flatpickr-day.inRange{border-radius:0;-webkit-box-shadow:-5px 0 0 #e6e6e6,5px 0 0 #e6e6e6;box-shadow:-5px 0 #e6e6e6,5px 0 #e6e6e6}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover,.flatpickr-day.prevMonthDay,.flatpickr-day.nextMonthDay,.flatpickr-day.notAllowed,.flatpickr-day.notAllowed.prevMonthDay,.flatpickr-day.notAllowed.nextMonthDay{color:#3939394d;background:transparent;border-color:transparent;cursor:default}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover{cursor:not-allowed;color:#3939391a}.flatpickr-day.week.selected{border-radius:0;-webkit-box-shadow:-5px 0 0 #569ff7,5px 0 0 #569ff7;box-shadow:-5px 0 #569ff7,5px 0 #569ff7}.flatpickr-day.hidden{visibility:hidden}.rangeMode .flatpickr-day{margin-top:1px}.flatpickr-weekwrapper{float:left}.flatpickr-weekwrapper .flatpickr-weeks{padding:0 12px;-webkit-box-shadow:1px 0 0 #e6e6e6;box-shadow:1px 0 #e6e6e6}.flatpickr-weekwrapper .flatpickr-weekday{float:none;width:100%;line-height:28px}.flatpickr-weekwrapper span.flatpickr-day,.flatpickr-weekwrapper span.flatpickr-day:hover{display:block;width:100%;max-width:none;color:#3939394d;background:transparent;cursor:default;border:none}.flatpickr-innerContainer{display:block;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden}.flatpickr-rContainer{display:inline-block;padding:0;-webkit-box-sizing:border-box;box-sizing:border-box}.flatpickr-time{text-align:center;outline:0;display:block;height:0;line-height:40px;max-height:40px;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.flatpickr-time:after{content:"";display:table;clear:both}.flatpickr-time .numInputWrapper{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;width:40%;height:40px;float:left}.flatpickr-time .numInputWrapper span.arrowUp:after{border-bottom-color:#393939}.flatpickr-time .numInputWrapper span.arrowDown:after{border-top-color:#393939}.flatpickr-time.hasSeconds .numInputWrapper{width:26%}.flatpickr-time.time24hr .numInputWrapper{width:49%}.flatpickr-time input{background:transparent;-webkit-box-shadow:none;box-shadow:none;border:0;border-radius:0;text-align:center;margin:0;padding:0;height:inherit;line-height:inherit;color:#393939;font-size:14px;position:relative;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield}.flatpickr-time input.flatpickr-hour{font-weight:700}.flatpickr-time input.flatpickr-minute,.flatpickr-time input.flatpickr-second{font-weight:400}.flatpickr-time input:focus{outline:0;border:0}.flatpickr-time .flatpickr-time-separator,.flatpickr-time .flatpickr-am-pm{height:inherit;float:left;line-height:inherit;color:#393939;font-weight:700;width:2%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-align-self:center;-ms-flex-item-align:center;align-self:center}.flatpickr-time .flatpickr-am-pm{outline:0;width:18%;cursor:pointer;text-align:center;font-weight:400}.flatpickr-time input:hover,.flatpickr-time .flatpickr-am-pm:hover,.flatpickr-time input:focus,.flatpickr-time .flatpickr-am-pm:focus{background:#eee}.flatpickr-input[readonly]{cursor:pointer}@-webkit-keyframes fpFadeInDown{0%{opacity:0;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}}@keyframes fpFadeInDown{0%{opacity:0;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}}:root{--tasks-details-icon: url("data:image/svg+xml;charset=utf-8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M8.59 16.58L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.42z'/></svg>")}ul.contains-task-list .task-list-item-checkbox{margin-inline-start:calc(var(--checkbox-size) * -1.5)!important}.plugin-tasks-query-explanation{--code-white-space: pre}.tasks-count{color:var(--text-faint);padding-left:20px}.tooltip.pop-up{animation:pop-up-animation .2s forwards ease-in-out}@keyframes pop-up-animation{0%{opacity:0;transform:translateY(-100%) scale(1)}20%{opacity:.7;transform:translateY(-100%) scale(1.02)}40%{opacity:1;transform:translateY(-100%) scale(1.05)}to{opacity:1;transform:translateY(-100%) scale(1)}}.task-cancelled,.task-created,.task-done,.task-due,.task-scheduled,.task-start{cursor:pointer;user-select:none;-webkit-user-select:none;-webkit-touch-callout:none}.tasks-edit,.tasks-postpone{width:1em;height:1em;vertical-align:middle;margin-left:.33em;cursor:pointer;font-family:var(--font-interface);color:var(--text-accent);user-select:none;-webkit-user-select:none;-webkit-touch-callout:none}a.tasks-edit,a.tasks-postpone{text-decoration:none}.tasks-edit:after{content:"\1f4dd"}.tasks-postpone:after{content:"\23e9"}.tasks-urgency{font-size:var(--font-ui-smaller);font-family:var(--font-interface);padding:2px 6px;border-radius:var(--radius-s);color:var(--text-normal);background-color:var(--background-secondary);margin-left:.5em;line-height:1}.internal-link.internal-link-short-mode{text-decoration:none}.tasks-list-text{position:relative}.tasks-list-text .tooltip{position:absolute;top:0;left:0;white-space:nowrap}.task-list-item-checkbox{cursor:pointer}.tasks-layout-hide-tags .task-description a.tag,.task-list-item .task-block-link{display:none}.tasks-modal section+section{margin-top:6px}.tasks-modal hr{margin:6px 0}.tasks-modal .tasks-modal-error{border:1px solid red!important}.tasks-modal .accesskey{text-decoration:underline;text-underline-offset:1pt}.tasks-modal-description-section textarea{width:100%;min-height:calc(var(--input-height) * 2);resize:vertical;margin-top:8px}.tasks-modal-priority-section{display:grid;grid-template-columns:6em auto auto auto;grid-row-gap:.15em}.tasks-modal-priority-section>label{grid-row-start:1;grid-row-end:3}.tasks-modal-priority-section .task-modal-priority-option-container{white-space:nowrap}.tasks-modal-priority-section .task-modal-priority-option-container input+label{font-size:var(--font-ui-small);border-radius:var(--input-radius);padding:2px 3px}.tasks-modal-priority-section .task-modal-priority-option-container input{accent-color:var(--interactive-accent)}.tasks-modal-priority-section .task-modal-priority-option-container input:focus+label{box-shadow:0 0 0 2px var(--background-modifier-border-focus);border-color:var(--background-modifier-border-focus)}.tasks-modal-priority-section .task-modal-priority-option-container input:checked+label{font-weight:700}.tasks-modal-priority-section .task-modal-priority-option-container input:not(:checked)+label>span:nth-child(4){filter:grayscale(100%) opacity(60%)}.tasks-modal-dates-section{display:grid;grid-template-columns:6em 13em auto;column-gap:.5em;row-gap:5px;align-items:center}.tasks-modal-dates-section label{grid-column:1}.tasks-modal-dates-section .tasks-modal-date-input{min-width:15em}.tasks-modal-dates-section .tasks-modal-date-editor-picker{margin-left:.5em}.tasks-modal-dates-section .tasks-modal-parsed-date{grid-column:3;font-size:var(--font-ui-small)}.tasks-modal-dates-section .future-dates-only{grid-column-start:1;grid-column-end:3}.tasks-modal-dates-section .future-dates-only input{margin-left:.67em;top:2px}.tasks-modal-dates-section .status-editor-status-selector{grid-column:2}.tasks-modal-dependencies-section{display:grid;grid-template-columns:6em auto;column-gap:.5em;row-gap:5px;align-items:center}.tasks-modal-dependencies-section .tasks-modal-dependency-input{grid-column:2;width:100%}.tasks-modal-dependencies-section .results-dependency{grid-column:2}.tasks-modal-button-section{position:sticky;bottom:0;background-color:var(--modal-background);padding-bottom:16px;padding-top:16px;display:grid;grid-template-columns:3fr 1fr;column-gap:.5em}.tasks-modal-button-section button:disabled{pointer-events:none!important;opacity:.3!important}@media (max-width: 649px){.tasks-modal-priority-section{grid-template-columns:6em auto auto}.tasks-modal-priority-section>label{grid-row:1/span 3}}@media (max-width: 499px){.tasks-modal-priority-section{grid-template-columns:4em auto auto}.tasks-modal-dates-section{grid-template-columns:1fr;grid-auto-columns:auto}.tasks-modal-dates-section .tasks-modal-date-input{grid-column:1}.tasks-modal-dates-section .tasks-modal-parsed-date{grid-column:2}.tasks-modal-dates-section .status-editor-status-selector,.tasks-modal-dependencies-section label,.tasks-modal-dependencies-section .results-dependency{grid-column:1}}@media (max-width: 399px){.tasks-modal-dates-section .status-editor-status-selector{grid-column:1}.tasks-modal-dates-section>.tasks-modal-parsed-date{grid-column:1}.tasks-modal-priority-section{grid-template-columns:4em auto}.tasks-modal-priority-section>label{grid-row:1/span 6}.tasks-modal-dependencies-section{grid-template-columns:1fr;grid-auto-columns:auto}}@media (max-width: 259px){.tasks-modal-priority-section{grid-template-columns:1fr}.tasks-modal-priority-section>label{grid-row:1}}.task-dependencies-container{grid-column:2;display:flex;flex-wrap:wrap;gap:8px}.task-dependency{display:inline-flex;background-color:var(--interactive-normal);box-shadow:var(--input-shadow);border-radius:28px;padding:4px 4px 4px 8px}.task-dependency-name{font-size:var(--font-ui-small);max-width:160px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.task-dependency-delete{padding:3px;cursor:pointer;height:inherit;box-shadow:none!important;border-radius:50%}.task-dependency-dropdown{list-style:none;position:absolute;top:0;left:0;padding:4px;margin:0;background-color:var(--background-primary);border:1px;border-radius:6px;border-color:var(--background-modifier-border);border-style:solid;z-index:99;max-height:170px;overflow-y:auto}.task-dependency-dropdown li{padding:5px;margin:2px;border-radius:6px;cursor:pointer;display:flex;justify-content:space-between}.task-dependency-dropdown li .dependency-name{overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.task-dependency-dropdown li .dependency-name-shared{width:60%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis}.task-dependency-dropdown li .dependency-path{width:40%;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-style:italic;text-align:right;color:var(--italic-color)}.task-dependency-dropdown li.selected{background-color:var(--text-selection)}.tasks-setting-important{color:red;font-weight:700}.tasks-settings-is-invalid{color:var(--text-error)!important;background-color:rgba(var(--background-modifier-error-rgb),.2)!important}.tasks-settings .additional{margin:6px 12px}.tasks-settings .additional>.setting-item{border-top:0;padding-top:9px}.tasks-settings details>summary{outline:none;display:block!important;list-style:none!important;list-style-type:none!important;min-height:1rem;border-top-left-radius:.1rem;border-top-right-radius:.1rem;cursor:pointer;position:relative}.tasks-settings details>summary::-webkit-details-marker,.tasks-settings details>summary::marker{display:none!important}.tasks-settings details>summary>.collapser{position:absolute;top:50%;right:8px;transform:translateY(-50%);content:""}.tasks-settings details>summary>.collapser>.handle{transform:rotate(0);transition:transform .25s;background-color:currentColor;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat;-webkit-mask-size:contain;mask-size:contain;-webkit-mask-image:var(--tasks-details-icon);mask-image:var(--tasks-details-icon);width:20px;height:20px}.tasks-settings details[open]>summary>.collapser>.handle{transform:rotate(90deg)}.tasks-nested-settings .setting-item{border:0px;padding-bottom:0}.tasks-nested-settings{padding-bottom:18px}.tasks-nested-settings[open] .setting-item-heading,.tasks-nested-settings:not(details) .setting-item-heading{border-top:0px;border-bottom:1px solid var(--background-modifier-border)}.tasks-settings .row-for-status{margin-top:0;margin-bottom:0}
 :root {
  --advanced-tables-helper-size: 28px;
}

.HyperMD-table-row span.cm-inline-code {
  font-size: 100%;
  padding: 0px;
}

.advanced-tables-buttons>div>.title {
  font-weight: var(--font-medium);
  font-size: var(--nav-item-size);
  color: var(--nav-item-color);
  text-decoration: underline;
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container {
  column-gap: 0.2rem;
  margin: 0.2rem 0 0.2rem 0;
  justify-content: start;
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container::before {
  min-width: 2.6rem;
  line-height: var(--advanced-tables-helper-size);
  font-size: var(--nav-item-size);
  font-weight: var(--nav-item-weight);
  color: var(--nav-item-color);
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container>* {
  height: var(--advanced-tables-helper-size);
  line-height: var(--advanced-tables-helper-size);
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container .nav-action-button {
  width: var(--advanced-tables-helper-size);
  height: var(--advanced-tables-helper-size);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: var(--radius-s);
}

[data-type="advanced-tables-toolbar"] .nav-buttons-container .nav-action-button:hover {
  background-color: var(--nav-item-background-hover);
  color: var(--nav-item-color-hover);
  font-weight: var(--nav-item-weight-hover);
}

.advanced-tables-row-label {
  width: 50px;
}

.widget-icon {
  width: 20px;
  height: 20px;
  fill: var(--text-muted);
}

.widget-icon:hover {
  fill: var(--text-normal);
}

.advanced-tables-csv-export textarea {
  height: 200px;
  width: 100%;
}

.advanced-tables-donation {
  width: 70%;
  margin: 0 auto;
  text-align: center;
}

.advanced-tables-donate-button {
  margin: 10px;
} .choices{position:relative;margin-bottom:24px;font-size:16px}.choices:focus{outline:none}.choices:last-child{margin-bottom:0}.choices.is-disabled .choices__inner,.choices.is-disabled .choices__input{background-color:#eaeaea;cursor:not-allowed;-webkit-user-select:none;-ms-user-select:none;user-select:none}.choices.is-disabled .choices__item{cursor:not-allowed}.choices [hidden]{display:none!important}.choices[data-type*=select-one]{cursor:pointer}.choices[data-type*=select-one] .choices__inner{padding-bottom:7.5px}.choices[data-type*=select-one] .choices__input{display:block;width:100%;padding:10px;border-bottom:1px solid #dddddd;background-color:#fff;margin:0}.choices[data-type*=select-one] .choices__button{background-image:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjMDAwIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==);padding:0;background-size:8px;position:absolute;top:50%;right:0;margin-top:-10px;margin-right:25px;height:20px;width:20px;border-radius:10em;opacity:.5}.choices[data-type*=select-one] .choices__button:hover,.choices[data-type*=select-one] .choices__button:focus{opacity:1}.choices[data-type*=select-one] .choices__button:focus{box-shadow:0 0 0 2px #00bcd4}.choices[data-type*=select-one] .choices__item[data-value=""] .choices__button{display:none}.choices[data-type*=select-one]:after{content:"";height:0;width:0;border-style:solid;border-color:#333333 transparent transparent transparent;border-width:5px;position:absolute;right:11.5px;top:50%;margin-top:-2.5px;pointer-events:none}.choices[data-type*=select-one].is-open:after{border-color:transparent transparent #333333 transparent;margin-top:-7.5px}.choices[data-type*=select-one][dir=rtl]:after{left:11.5px;right:auto}.choices[data-type*=select-one][dir=rtl] .choices__button{right:auto;left:0;margin-left:25px;margin-right:0}.choices[data-type*=select-multiple] .choices__inner,.choices[data-type*=text] .choices__inner{cursor:text}.choices[data-type*=select-multiple] .choices__button,.choices[data-type*=text] .choices__button{position:relative;display:inline-block;margin:0 -4px 0 8px;padding-left:16px;border-left:1px solid #008fa1;background-image:url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjEiIGhlaWdodD0iMjEiIHZpZXdCb3g9IjAgMCAyMSAyMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSIjRkZGIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0yLjU5Mi4wNDRsMTguMzY0IDE4LjM2NC0yLjU0OCAyLjU0OEwuMDQ0IDIuNTkyeiIvPjxwYXRoIGQ9Ik0wIDE4LjM2NEwxOC4zNjQgMGwyLjU0OCAyLjU0OEwyLjU0OCAyMC45MTJ6Ii8+PC9nPjwvc3ZnPg==);background-size:8px;width:8px;line-height:1;opacity:.75;border-radius:0}.choices[data-type*=select-multiple] .choices__button:hover,.choices[data-type*=select-multiple] .choices__button:focus,.choices[data-type*=text] .choices__button:hover,.choices[data-type*=text] .choices__button:focus{opacity:1}.choices__inner{display:inline-block;vertical-align:top;width:100%;background-color:#f9f9f9;padding:7.5px 7.5px 3.75px;border:1px solid #dddddd;border-radius:2.5px;font-size:14px;min-height:44px;overflow:hidden}.is-focused .choices__inner,.is-open .choices__inner{border-color:#b7b7b7}.is-open .choices__inner{border-radius:2.5px 2.5px 0 0}.is-flipped.is-open .choices__inner{border-radius:0 0 2.5px 2.5px}.choices__list{margin:0;padding-left:0;list-style:none}.choices__list--single{display:inline-block;padding:4px 16px 4px 4px;width:100%}[dir=rtl] .choices__list--single{padding-right:4px;padding-left:16px}.choices__list--single .choices__item{width:100%}.choices__list--multiple{display:inline}.choices__list--multiple .choices__item{display:inline-block;vertical-align:middle;border-radius:20px;padding:4px 10px;font-size:12px;font-weight:500;margin-right:3.75px;margin-bottom:3.75px;background-color:#00bcd4;border:1px solid #00a5bb;color:#fff;word-break:break-all;box-sizing:border-box}.choices__list--multiple .choices__item[data-deletable]{padding-right:5px}[dir=rtl] .choices__list--multiple .choices__item{margin-right:0;margin-left:3.75px}.choices__list--multiple .choices__item.is-highlighted{background-color:#00a5bb;border:1px solid #008fa1}.is-disabled .choices__list--multiple .choices__item{background-color:#aaa;border:1px solid #919191}.choices__list--dropdown{visibility:hidden;z-index:1;position:absolute;width:100%;background-color:#fff;border:1px solid #dddddd;top:100%;margin-top:-1px;border-bottom-left-radius:2.5px;border-bottom-right-radius:2.5px;overflow:hidden;word-break:break-all;will-change:visibility}.choices__list--dropdown.is-active{visibility:visible}.is-open .choices__list--dropdown{border-color:#b7b7b7}.is-flipped .choices__list--dropdown{top:auto;bottom:100%;margin-top:0;margin-bottom:-1px;border-radius:.25rem .25rem 0 0}.choices__list--dropdown .choices__list{position:relative;max-height:300px;overflow:auto;-webkit-overflow-scrolling:touch;will-change:scroll-position}.choices__list--dropdown .choices__item{position:relative;padding:10px;font-size:14px}[dir=rtl] .choices__list--dropdown .choices__item{text-align:right}@media (min-width: 640px){.choices__list--dropdown .choices__item--selectable{padding-right:100px}.choices__list--dropdown .choices__item--selectable:after{content:attr(data-select-text);font-size:12px;opacity:0;position:absolute;right:10px;top:50%;transform:translateY(-50%)}[dir=rtl] .choices__list--dropdown .choices__item--selectable{text-align:right;padding-left:100px;padding-right:10px}[dir=rtl] .choices__list--dropdown .choices__item--selectable:after{right:auto;left:10px}}.choices__list--dropdown .choices__item--selectable.is-highlighted{background-color:#f2f2f2}.choices__list--dropdown .choices__item--selectable.is-highlighted:after{opacity:.5}.choices__item{cursor:default}.choices__item--selectable{cursor:pointer}.choices__item--disabled{cursor:not-allowed;-webkit-user-select:none;-ms-user-select:none;user-select:none;opacity:.5}.choices__heading{font-weight:600;font-size:12px;padding:10px;border-bottom:1px solid #f7f7f7;color:gray}.choices__button{text-indent:-9999px;-webkit-appearance:none;-moz-appearance:none;appearance:none;border:0;background-color:transparent;background-repeat:no-repeat;background-position:center;cursor:pointer}.choices__button:focus{outline:none}.choices__input{display:inline-block;vertical-align:baseline;background-color:#f9f9f9;font-size:14px;margin-bottom:5px;border:0;border-radius:0;max-width:100%;padding:4px 0 4px 2px}.choices__input:focus{outline:0}[dir=rtl] .choices__input{padding-right:2px;padding-left:0}.choices__placeholder{opacity:.5}.flatpickr-calendar{background:transparent;opacity:0;display:none;text-align:center;visibility:hidden;padding:0;-webkit-animation:none;animation:none;direction:ltr;border:0;font-size:14px;line-height:24px;border-radius:5px;position:absolute;width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;-ms-touch-action:manipulation;touch-action:manipulation;background:#fff;-webkit-box-shadow:1px 0 0 #e6e6e6,-1px 0 0 #e6e6e6,0 1px 0 #e6e6e6,0 -1px 0 #e6e6e6,0 3px 13px rgba(0,0,0,.08);box-shadow:1px 0 #e6e6e6,-1px 0 #e6e6e6,0 1px #e6e6e6,0 -1px #e6e6e6,0 3px 13px #00000014}.flatpickr-calendar.open,.flatpickr-calendar.inline{opacity:1;max-height:640px;visibility:visible}.flatpickr-calendar.open{display:inline-block;z-index:99999}.flatpickr-calendar.animate.open{-webkit-animation:fpFadeInDown .3s cubic-bezier(.23,1,.32,1);animation:fpFadeInDown .3s cubic-bezier(.23,1,.32,1)}.flatpickr-calendar.inline{display:block;position:relative;top:2px}.flatpickr-calendar.static{position:absolute;top:calc(100% + 2px)}.flatpickr-calendar.static.open{z-index:999;display:block}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+1) .flatpickr-day.inRange:nth-child(7n+7){-webkit-box-shadow:none!important;box-shadow:none!important}.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+2) .flatpickr-day.inRange:nth-child(7n+1){-webkit-box-shadow:-2px 0 0 #e6e6e6,5px 0 0 #e6e6e6;box-shadow:-2px 0 #e6e6e6,5px 0 #e6e6e6}.flatpickr-calendar .hasWeeks .dayContainer,.flatpickr-calendar .hasTime .dayContainer{border-bottom:0;border-bottom-right-radius:0;border-bottom-left-radius:0}.flatpickr-calendar .hasWeeks .dayContainer{border-left:0}.flatpickr-calendar.hasTime .flatpickr-time{height:40px;border-top:1px solid #e6e6e6}.flatpickr-calendar.noCalendar.hasTime .flatpickr-time{height:auto}.flatpickr-calendar:before,.flatpickr-calendar:after{position:absolute;display:block;pointer-events:none;border:solid transparent;content:"";height:0;width:0;left:22px}.flatpickr-calendar.rightMost:before,.flatpickr-calendar.arrowRight:before,.flatpickr-calendar.rightMost:after,.flatpickr-calendar.arrowRight:after{left:auto;right:22px}.flatpickr-calendar.arrowCenter:before,.flatpickr-calendar.arrowCenter:after{left:50%;right:50%}.flatpickr-calendar:before{border-width:5px;margin:0 -5px}.flatpickr-calendar:after{border-width:4px;margin:0 -4px}.flatpickr-calendar.arrowTop:before,.flatpickr-calendar.arrowTop:after{bottom:100%}.flatpickr-calendar.arrowTop:before{border-bottom-color:#e6e6e6}.flatpickr-calendar.arrowTop:after{border-bottom-color:#fff}.flatpickr-calendar.arrowBottom:before,.flatpickr-calendar.arrowBottom:after{top:100%}.flatpickr-calendar.arrowBottom:before{border-top-color:#e6e6e6}.flatpickr-calendar.arrowBottom:after{border-top-color:#fff}.flatpickr-calendar:focus{outline:0}.flatpickr-wrapper{position:relative;display:inline-block}.flatpickr-months{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.flatpickr-months .flatpickr-month{background:transparent;color:#000000e6;fill:#000000e6;height:34px;line-height:1;text-align:center;position:relative;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;overflow:hidden;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}.flatpickr-months .flatpickr-prev-month,.flatpickr-months .flatpickr-next-month{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;text-decoration:none;cursor:pointer;position:absolute;top:0;height:34px;padding:10px;z-index:3;color:#000000e6;fill:#000000e6}.flatpickr-months .flatpickr-prev-month.flatpickr-disabled,.flatpickr-months .flatpickr-next-month.flatpickr-disabled{display:none}.flatpickr-months .flatpickr-prev-month i,.flatpickr-months .flatpickr-next-month i{position:relative}.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,.flatpickr-months .flatpickr-next-month.flatpickr-prev-month{left:0}.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,.flatpickr-months .flatpickr-next-month.flatpickr-next-month{right:0}.flatpickr-months .flatpickr-prev-month:hover,.flatpickr-months .flatpickr-next-month:hover{color:#959ea9}.flatpickr-months .flatpickr-prev-month:hover svg,.flatpickr-months .flatpickr-next-month:hover svg{fill:#f64747}.flatpickr-months .flatpickr-prev-month svg,.flatpickr-months .flatpickr-next-month svg{width:14px;height:14px}.flatpickr-months .flatpickr-prev-month svg path,.flatpickr-months .flatpickr-next-month svg path{-webkit-transition:fill .1s;transition:fill .1s;fill:inherit}.numInputWrapper{position:relative;height:auto}.numInputWrapper input,.numInputWrapper span{display:inline-block}.numInputWrapper input{width:100%}.numInputWrapper input::-ms-clear{display:none}.numInputWrapper input::-webkit-outer-spin-button,.numInputWrapper input::-webkit-inner-spin-button{margin:0;-webkit-appearance:none}.numInputWrapper span{position:absolute;right:0;width:14px;padding:0 4px 0 2px;height:50%;line-height:50%;opacity:0;cursor:pointer;border:1px solid rgba(57,57,57,.15);-webkit-box-sizing:border-box;box-sizing:border-box}.numInputWrapper span:hover{background:#0000001a}.numInputWrapper span:active{background:#0003}.numInputWrapper span:after{display:block;content:"";position:absolute}.numInputWrapper span.arrowUp{top:0;border-bottom:0}.numInputWrapper span.arrowUp:after{border-left:4px solid transparent;border-right:4px solid transparent;border-bottom:4px solid rgba(57,57,57,.6);top:26%}.numInputWrapper span.arrowDown{top:50%}.numInputWrapper span.arrowDown:after{border-left:4px solid transparent;border-right:4px solid transparent;border-top:4px solid rgba(57,57,57,.6);top:40%}.numInputWrapper span svg{width:inherit;height:auto}.numInputWrapper span svg path{fill:#00000080}.numInputWrapper:hover{background:#0000000d}.numInputWrapper:hover span{opacity:1}.flatpickr-current-month{font-size:135%;line-height:inherit;font-weight:300;color:inherit;position:absolute;width:75%;left:12.5%;padding:7.48px 0 0;line-height:1;height:34px;display:inline-block;text-align:center;-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}.flatpickr-current-month span.cur-month{font-family:inherit;font-weight:700;color:inherit;display:inline-block;margin-left:.5ch;padding:0}.flatpickr-current-month span.cur-month:hover{background:#0000000d}.flatpickr-current-month .numInputWrapper{width:6ch;width:7ch\fffd;display:inline-block}.flatpickr-current-month .numInputWrapper span.arrowUp:after{border-bottom-color:#000000e6}.flatpickr-current-month .numInputWrapper span.arrowDown:after{border-top-color:#000000e6}.flatpickr-current-month input.cur-year{background:transparent;-webkit-box-sizing:border-box;box-sizing:border-box;color:inherit;cursor:text;padding:0 0 0 .5ch;margin:0;display:inline-block;font-size:inherit;font-family:inherit;font-weight:300;line-height:inherit;height:auto;border:0;border-radius:0;vertical-align:initial;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield}.flatpickr-current-month input.cur-year:focus{outline:0}.flatpickr-current-month input.cur-year[disabled],.flatpickr-current-month input.cur-year[disabled]:hover{font-size:100%;color:#00000080;background:transparent;pointer-events:none}.flatpickr-current-month .flatpickr-monthDropdown-months{appearance:menulist;background:transparent;border:none;border-radius:0;box-sizing:border-box;color:inherit;cursor:pointer;font-size:inherit;font-family:inherit;font-weight:300;height:auto;line-height:inherit;margin:-1px 0 0;outline:none;padding:0 0 0 .5ch;position:relative;vertical-align:initial;-webkit-box-sizing:border-box;-webkit-appearance:menulist;-moz-appearance:menulist;width:auto}.flatpickr-current-month .flatpickr-monthDropdown-months:focus,.flatpickr-current-month .flatpickr-monthDropdown-months:active{outline:none}.flatpickr-current-month .flatpickr-monthDropdown-months:hover{background:#0000000d}.flatpickr-current-month .flatpickr-monthDropdown-months .flatpickr-monthDropdown-month{background-color:transparent;outline:none;padding:0}.flatpickr-weekdays{background:transparent;text-align:center;overflow:hidden;width:100%;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:center;-webkit-align-items:center;-ms-flex-align:center;align-items:center;height:28px}.flatpickr-weekdays .flatpickr-weekdaycontainer{display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1}span.flatpickr-weekday{cursor:default;font-size:90%;background:transparent;color:#0000008a;line-height:1;margin:0;text-align:center;display:block;-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;font-weight:bolder}.dayContainer,.flatpickr-weeks{padding:1px 0 0}.flatpickr-days{position:relative;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-align:start;-webkit-align-items:flex-start;-ms-flex-align:start;align-items:flex-start;width:307.875px}.flatpickr-days:focus{outline:0}.dayContainer{padding:0;outline:0;text-align:left;width:307.875px;min-width:307.875px;max-width:307.875px;-webkit-box-sizing:border-box;box-sizing:border-box;display:inline-block;display:-ms-flexbox;display:-webkit-box;display:-webkit-flex;display:flex;-webkit-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-wrap:wrap;-ms-flex-pack:justify;-webkit-justify-content:space-around;justify-content:space-around;-webkit-transform:translate3d(0,0,0);transform:translateZ(0);opacity:1}.dayContainer+.dayContainer{-webkit-box-shadow:-1px 0 0 #e6e6e6;box-shadow:-1px 0 #e6e6e6}.flatpickr-day{background:none;border:1px solid transparent;border-radius:150px;-webkit-box-sizing:border-box;box-sizing:border-box;color:#393939;cursor:pointer;font-weight:400;width:14.2857143%;-webkit-flex-basis:14.2857143%;-ms-flex-preferred-size:14.2857143%;flex-basis:14.2857143%;max-width:39px;height:39px;line-height:39px;margin:0;display:inline-block;position:relative;-webkit-box-pack:center;-webkit-justify-content:center;-ms-flex-pack:center;justify-content:center;text-align:center}.flatpickr-day.inRange,.flatpickr-day.prevMonthDay.inRange,.flatpickr-day.nextMonthDay.inRange,.flatpickr-day.today.inRange,.flatpickr-day.prevMonthDay.today.inRange,.flatpickr-day.nextMonthDay.today.inRange,.flatpickr-day:hover,.flatpickr-day.prevMonthDay:hover,.flatpickr-day.nextMonthDay:hover,.flatpickr-day:focus,.flatpickr-day.prevMonthDay:focus,.flatpickr-day.nextMonthDay:focus{cursor:pointer;outline:0;background:#e6e6e6;border-color:#e6e6e6}.flatpickr-day.today{border-color:#959ea9}.flatpickr-day.today:hover,.flatpickr-day.today:focus{border-color:#959ea9;background:#959ea9;color:#fff}.flatpickr-day.selected,.flatpickr-day.startRange,.flatpickr-day.endRange,.flatpickr-day.selected.inRange,.flatpickr-day.startRange.inRange,.flatpickr-day.endRange.inRange,.flatpickr-day.selected:focus,.flatpickr-day.startRange:focus,.flatpickr-day.endRange:focus,.flatpickr-day.selected:hover,.flatpickr-day.startRange:hover,.flatpickr-day.endRange:hover,.flatpickr-day.selected.prevMonthDay,.flatpickr-day.startRange.prevMonthDay,.flatpickr-day.endRange.prevMonthDay,.flatpickr-day.selected.nextMonthDay,.flatpickr-day.startRange.nextMonthDay,.flatpickr-day.endRange.nextMonthDay{background:#569ff7;-webkit-box-shadow:none;box-shadow:none;color:#fff;border-color:#569ff7}.flatpickr-day.selected.startRange,.flatpickr-day.startRange.startRange,.flatpickr-day.endRange.startRange{border-radius:50px 0 0 50px}.flatpickr-day.selected.endRange,.flatpickr-day.startRange.endRange,.flatpickr-day.endRange.endRange{border-radius:0 50px 50px 0}.flatpickr-day.selected.startRange+.endRange:not(:nth-child(7n+1)),.flatpickr-day.startRange.startRange+.endRange:not(:nth-child(7n+1)),.flatpickr-day.endRange.startRange+.endRange:not(:nth-child(7n+1)){-webkit-box-shadow:-10px 0 0 #569ff7;box-shadow:-10px 0 #569ff7}.flatpickr-day.selected.startRange.endRange,.flatpickr-day.startRange.startRange.endRange,.flatpickr-day.endRange.startRange.endRange{border-radius:50px}.flatpickr-day.inRange{border-radius:0;-webkit-box-shadow:-5px 0 0 #e6e6e6,5px 0 0 #e6e6e6;box-shadow:-5px 0 #e6e6e6,5px 0 #e6e6e6}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover,.flatpickr-day.prevMonthDay,.flatpickr-day.nextMonthDay,.flatpickr-day.notAllowed,.flatpickr-day.notAllowed.prevMonthDay,.flatpickr-day.notAllowed.nextMonthDay{color:#3939394d;background:transparent;border-color:transparent;cursor:default}.flatpickr-day.flatpickr-disabled,.flatpickr-day.flatpickr-disabled:hover{cursor:not-allowed;color:#3939391a}.flatpickr-day.week.selected{border-radius:0;-webkit-box-shadow:-5px 0 0 #569ff7,5px 0 0 #569ff7;box-shadow:-5px 0 #569ff7,5px 0 #569ff7}.flatpickr-day.hidden{visibility:hidden}.rangeMode .flatpickr-day{margin-top:1px}.flatpickr-weekwrapper{float:left}.flatpickr-weekwrapper .flatpickr-weeks{padding:0 12px;-webkit-box-shadow:1px 0 0 #e6e6e6;box-shadow:1px 0 #e6e6e6}.flatpickr-weekwrapper .flatpickr-weekday{float:none;width:100%;line-height:28px}.flatpickr-weekwrapper span.flatpickr-day,.flatpickr-weekwrapper span.flatpickr-day:hover{display:block;width:100%;max-width:none;color:#3939394d;background:transparent;cursor:default;border:none}.flatpickr-innerContainer{display:block;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden}.flatpickr-rContainer{display:inline-block;padding:0;-webkit-box-sizing:border-box;box-sizing:border-box}.flatpickr-time{text-align:center;outline:0;display:block;height:0;line-height:40px;max-height:40px;-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex}.flatpickr-time:after{content:"";display:table;clear:both}.flatpickr-time .numInputWrapper{-webkit-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;width:40%;height:40px;float:left}.flatpickr-time .numInputWrapper span.arrowUp:after{border-bottom-color:#393939}.flatpickr-time .numInputWrapper span.arrowDown:after{border-top-color:#393939}.flatpickr-time.hasSeconds .numInputWrapper{width:26%}.flatpickr-time.time24hr .numInputWrapper{width:49%}.flatpickr-time input{background:transparent;-webkit-box-shadow:none;box-shadow:none;border:0;border-radius:0;text-align:center;margin:0;padding:0;height:inherit;line-height:inherit;color:#393939;font-size:14px;position:relative;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-appearance:textfield;-moz-appearance:textfield;appearance:textfield}.flatpickr-time input.flatpickr-hour{font-weight:700}.flatpickr-time input.flatpickr-minute,.flatpickr-time input.flatpickr-second{font-weight:400}.flatpickr-time input:focus{outline:0;border:0}.flatpickr-time .flatpickr-time-separator,.flatpickr-time .flatpickr-am-pm{height:inherit;float:left;line-height:inherit;color:#393939;font-weight:700;width:2%;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;-webkit-align-self:center;-ms-flex-item-align:center;align-self:center}.flatpickr-time .flatpickr-am-pm{outline:0;width:18%;cursor:pointer;text-align:center;font-weight:400}.flatpickr-time input:hover,.flatpickr-time .flatpickr-am-pm:hover,.flatpickr-time input:focus,.flatpickr-time .flatpickr-am-pm:focus{background:#eee}.flatpickr-input[readonly]{cursor:pointer}@-webkit-keyframes fpFadeInDown{0%{opacity:0;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}}@keyframes fpFadeInDown{0%{opacity:0;-webkit-transform:translate3d(0,-20px,0);transform:translate3d(0,-20px,0)}to{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translateZ(0)}}.workspace-leaf-content[data-type=kanban] .view-content{padding:0}.workspace-leaf-content[data-type=kanban]>.view-header{display:flex!important}.kanban-plugin{--lane-width: 272px}.kanban-plugin{contain:content;height:100%;width:100%;position:relative;display:flex;flex-direction:column}.kanban-plugin a.tag,.kanban-plugin__drag-container a.tag{padding-inline:var(--tag-padding-x);padding-block:var(--tag-padding-y)}.kanban-plugin__table-wrapper{height:100%;width:100%;overflow:auto;padding-block-end:40px;--table-column-first-border-width: 0;--table-column-last-border-width: 0;--table-row-last-border-width: 0}.kanban-plugin__table-wrapper table{width:fit-content;margin-block:0;margin-inline:auto;box-shadow:0 0 0 var(--table-border-width) var(--table-border-color)}.kanban-plugin__table-wrapper tr{width:fit-content}.kanban-plugin__table-wrapper th,.kanban-plugin__table-wrapper td{text-align:start;vertical-align:top;font-size:.875rem;padding:0!important;height:1px}.kanban-plugin__table-wrapper th.mod-has-icon .kanban-plugin__table-cell-wrapper,.kanban-plugin__table-wrapper td.mod-has-icon .kanban-plugin__table-cell-wrapper{padding-inline-end:var(--size-2-2)}.kanban-plugin__table-wrapper th .kanban-plugin__table-cell-wrapper,.kanban-plugin__table-wrapper td .kanban-plugin__table-cell-wrapper{height:100%;padding-inline:var(--size-4-2);padding-block:var(--size-2-2)}.kanban-plugin__table-wrapper th .kanban-plugin__item-prefix-button-wrapper input[type=checkbox],.kanban-plugin__table-wrapper td .kanban-plugin__item-prefix-button-wrapper input[type=checkbox]{margin-block:2px}.kanban-plugin__table-wrapper th:has(.markdown-source-view),.kanban-plugin__table-wrapper td:has(.markdown-source-view){--background-primary: var(--background-primary-alt);background:var(--background-primary);outline:2px solid var(--background-modifier-border-focus)}.kanban-plugin__table-wrapper thead tr>th{height:1px;background-color:var(--background-primary);position:sticky;top:0;z-index:1;overflow:visible}.kanban-plugin__table-wrapper thead tr>th:nth-child(2n+2){background-color:var(--background-primary)}.kanban-plugin__table-wrapper thead tr>th .kanban-plugin__table-cell-wrapper{height:100%;padding-block:var(--size-2-2);padding-inline:var(--size-4-2) var(--size-2-2);box-shadow:0 0 0 var(--table-border-width) var(--table-border-color)}.kanban-plugin__table-wrapper .resizer{position:absolute;top:0;height:100%;width:5px;background:var(--table-selection-border-color);cursor:col-resize;user-select:none;touch-action:none}.kanban-plugin__table-wrapper .resizer.ltr{right:0}.kanban-plugin__table-wrapper .resizer.rtl{left:0}.kanban-plugin__table-wrapper .resizer.isResizing{opacity:1}@media (hover: hover){.kanban-plugin__table-wrapper .resizer{opacity:0}.kanban-plugin__table-wrapper .resizer:hover{opacity:1}}.kanban-plugin__table-wrapper .kanban-plugin__item-tags:not(:empty){margin-block-start:-5px}.kanban-plugin__table-wrapper .kanban-plugin__item-metadata-date-relative{display:block}.kanban-plugin__table-wrapper .kanban-plugin__item-input-wrapper,.kanban-plugin__table-wrapper .cm-table-widget,.kanban-plugin__table-wrapper .kanban-plugin__item-title,.kanban-plugin__table-wrapper .kanban-plugin__item-title-wrapper,.kanban-plugin__table-wrapper .kanban-plugin__item-content-wrapper{height:100%}.kanban-plugin__table-wrapper .kanban-plugin__item-title-wrapper{padding:0}.kanban-plugin .markdown-source-view.mod-cm6{display:block;font-size:.875rem}.kanban-plugin .markdown-source-view.mod-cm6 .cm-scroller{overflow:visible}.kanban-plugin__table-header{display:flex;gap:var(--size-4-2);align-items:center;justify-content:space-between}.kanban-plugin__table-header-sort{line-height:1;color:var(--text-faint);padding:2px;border-radius:4px}.kanban-plugin__table-header-sort>span{display:flex}div:hover>.kanban-plugin__table-header-sort{background-color:var(--background-modifier-hover)}.kanban-plugin__cell-flex-wrapper{display:flex;gap:8px;align-items:flex-start;justify-content:space-between}.kanban-plugin__cell-flex-wrapper .lucide-more-vertical{transform:none}.kanban-plugin__icon-wrapper{display:flex;line-height:1}.kanban-plugin__icon-wrapper>.kanban-plugin__icon{display:flex}.kanban-plugin.something-is-dragging{cursor:grabbing;cursor:-webkit-grabbing}.kanban-plugin.something-is-dragging *{pointer-events:none}.kanban-plugin__item button,.kanban-plugin__lane button,.kanban-plugin button{line-height:1;margin:0;transition:.1s color,.1s background-color}.kanban-plugin__search-wrapper{width:100%;position:sticky;top:0;left:0;padding-block:10px;padding-inline:13px;display:flex;justify-content:flex-end;align-items:center;z-index:2;background-color:var(--background-primary)}button.kanban-plugin__search-cancel-button{display:flex;line-height:1;padding:6px;border:1px solid var(--background-modifier-border);background:var(--background-secondary-alt);color:var(--text-muted);margin-block:0;margin-inline:5px 0;font-size:16px}button.kanban-plugin__search-cancel-button .kanban-plugin__icon{display:flex}.kanban-plugin__icon{display:inline-block;line-height:1;--icon-size: 1em}.kanban-plugin__board{display:flex;width:100%;height:100%}.kanban-plugin__board>div{display:flex;align-items:flex-start;justify-content:flex-start;padding:1rem;width:fit-content;height:100%}.kanban-plugin__board.kanban-plugin__vertical>div{height:fit-content;width:100%;flex-direction:column}.is-mobile .view-content:not(.is-mobile-editing) .kanban-plugin__board>div{padding-bottom:calc(1rem + var(--mobile-navbar-height))}.kanban-plugin__board.is-adding-lane>div{padding-inline-end:calc(250px + 1rem)}.kanban-plugin__lane-wrapper{display:flex;flex-shrink:0;margin-inline-end:10px;max-height:100%;width:var(--lane-width)}.kanban-plugin__vertical .kanban-plugin__lane-wrapper{margin-block-end:10px;margin-inline-end:0}.kanban-plugin__lane{width:100%;display:flex;flex-direction:column;background-color:var(--background-secondary);border-radius:6px;border:1px solid var(--background-modifier-border)}.is-dropping>.kanban-plugin__lane{background-color:hsla(var(--interactive-accent-hsl),.15);border-color:hsla(var(--interactive-accent-hsl),1);outline:1px solid hsla(var(--interactive-accent-hsl),1)}.kanban-plugin__placeholder.kanban-plugin__lane-placeholder{height:100%;flex-grow:1;margin-inline-end:5px}.kanban-plugin__lane.is-hidden{display:none}.kanban-plugin__lane button{padding-block:8px;padding-inline:10px}.kanban-plugin__lane-form-wrapper{position:absolute;top:1rem;right:1rem;width:250px;background-color:var(--background-secondary);border-radius:6px;border:2px solid hsla(var(--interactive-accent-hsl),.7);z-index:var(--layer-popover);box-shadow:0 .5px 1px .5px #0000001a,0 2px 10px #0000001a,0 10px 20px #0000001a}.kanban-plugin__lane-input{--font-text-size: var(--font-ui-small);padding-block:var(--size-4-1);padding-inline:var(--size-4-2);background-color:var(--background-primary);border-radius:var(--radius-s)}.kanban-plugin__lane-input-wrapper{padding:10px}.kanban-plugin__item-input-actions,.kanban-plugin__lane-input-actions{display:flex;align-items:flex-start;justify-content:flex-start;padding-block:0 10px;padding-inline:10px}.kanban-plugin__item-input-actions button,.kanban-plugin__lane-input-actions button{display:block;margin-inline-end:5px}button.kanban-plugin__item-action-add,button.kanban-plugin__lane-action-add{background-color:var(--interactive-accent);color:var(--text-on-accent)}button.kanban-plugin__item-action-add:hover,button.kanban-plugin__lane-action-add:hover{background-color:var(--interactive-accent-hover)}.kanban-plugin__lane-header-wrapper{padding-block:8px;padding-inline:8px 12px;display:flex;align-items:center;gap:var(--size-4-1);flex-shrink:0;flex-grow:0;border-bottom:1px solid var(--background-modifier-border)}.collapse-horizontal .kanban-plugin__lane-header-wrapper,.collapse-vertical .kanban-plugin__lane-header-wrapper,.will-prepend .kanban-plugin__lane-header-wrapper{border-bottom:none}.kanban-plugin__lane-wrapper.collapse-horizontal{width:auto}.kanban-plugin__lane-wrapper.collapse-horizontal .kanban-plugin__lane-header-wrapper{writing-mode:vertical-lr}.kanban-plugin__lane-wrapper.collapse-horizontal .kanban-plugin__lane-header-wrapper{gap:var(--size-4-2)}.kanban-plugin__lane-wrapper.collapse-horizontal .kanban-plugin__lane-title-count,.kanban-plugin__lane-wrapper.collapse-horizontal .kanban-plugin__lane-title-text{transform:rotate(180deg)}.kanban-plugin__lane-wrapper.collapse-horizontal .kanban-plugin__lane-settings-button-wrapper{display:none}.kanban-plugin__lane-wrapper.collapse-vertical .kanban-plugin__lane-settings-button-wrapper{visibility:hidden}.kanban-plugin__lane-collapse{flex-grow:0;color:var(--text-faint)}.kanban-plugin__lane-collapse>span{display:flex}.collapse-vertical .kanban-plugin__lane-collapse>span{transform:rotate(-90deg)}.kanban-plugin__lane-grip{cursor:grab;flex-grow:0;color:var(--text-faint)}.kanban-plugin__lane-grip:active{cursor:grabbing}.kanban-plugin__lane-collapse svg{--icon-size: 1rem}.kanban-plugin__lane-grip>svg{height:1rem;display:block}.kanban-plugin__lane-title{font-weight:600;font-size:.875rem;flex-grow:1;width:100%;display:flex;flex-direction:column}.kanban-plugin__lane-title-text{flex-grow:1}div.kanban-plugin__lane-title-count{border-radius:3px;color:var(--text-muted);display:block;font-size:13px;line-height:1;padding:4px}div.kanban-plugin__lane-title-count.wip-exceeded{font-weight:700;color:var(--text-normal);background-color:rgba(var(--background-modifier-error-rgb),.25)}.kanban-plugin__table-cell-wrapper .kanban-plugin__lane-menu,.kanban-plugin__table-cell-wrapper .kanban-plugin__item-prefix-button,.kanban-plugin__item .kanban-plugin__item-prefix-button,.kanban-plugin__item .kanban-plugin__item-postfix-button,.kanban-plugin__lane .kanban-plugin__lane-settings-button{--icon-stroke: 2.5px;font-size:13px;line-height:1;color:var(--text-muted);padding:4px;display:flex;margin-inline-end:-4px}.kanban-plugin__table-cell-wrapper .kanban-plugin__lane-menu.is-enabled,.kanban-plugin__table-cell-wrapper .kanban-plugin__item-prefix-button.is-enabled,.kanban-plugin__item .kanban-plugin__item-prefix-button.is-enabled,.kanban-plugin__item .kanban-plugin__item-postfix-button.is-enabled,.kanban-plugin__lane .kanban-plugin__lane-settings-button.is-enabled{color:var(--text-accent)}.kanban-plugin__table-cell-wrapper .kanban-plugin__lane-menu{color:var(--text-faint);margin-inline-start:2px;margin-inline-end:0px}.kanban-plugin__table-cell-wrapper .kanban-plugin__item-prefix-button,.kanban-plugin__item .kanban-plugin__item-prefix-button{margin-inline-end:4px;margin-inline-start:-4px}.kanban-plugin__table-cell-wrapper button.kanban-plugin__item-prefix-button,.kanban-plugin__item button.kanban-plugin__item-prefix-button{margin-block:4px;margin-inline:0 7px;padding:0}.kanban-plugin__lane-action-wrapper,.kanban-plugin__item-edit-archive-button,.kanban-plugin__item-settings-actions .kanban-plugin__icon,.kanban-plugin__item-edit-archive-button>.kanban-plugin__icon,.kanban-plugin__item-prefix-button>.kanban-plugin__icon,.kanban-plugin__item-postfix-button>.kanban-plugin__icon,.kanban-plugin__lane-settings-button>.kanban-plugin__icon{display:flex}.kanban-plugin__lane-settings-button-wrapper{display:flex;gap:4px}button.kanban-plugin__lane-settings-button+button.kanban-plugin__lane-settings-button{margin-inline-start:2px}.kanban-plugin__lane-settings-button svg{width:1em;height:1em}.kanban-plugin__lane-items-wrapper{margin:4px;height:100%}.kanban-plugin__lane-items{padding:4px;margin-block:0;margin-inline:4px;display:flex;flex-direction:column}.kanban-plugin__lane-items>div{margin-block-start:4px}.kanban-plugin__lane-items>.kanban-plugin__placeholder{flex-grow:1}.kanban-plugin__lane-items>.kanban-plugin__placeholder:only-child{height:2.55em;border:3px dashed rgba(var(--text-muted-rgb),.1);margin-block-end:4px;border-radius:6px;transition:border .2s ease}.is-sorting .kanban-plugin__lane-items>.kanban-plugin__placeholder:only-child{border-color:hsla(var(--interactive-accent-hsl),.6)}.kanban-plugin__item-button-wrapper{border-top:1px solid var(--background-modifier-border);padding:8px;flex-shrink:0;flex-grow:0}.kanban-plugin__item-button-wrapper>button{text-align:left;width:100%}.kanban-plugin__lane-header-wrapper+.kanban-plugin__item-button-wrapper{border-top:none;border-bottom:1px solid var(--background-modifier-border);padding-block:4px 8px;padding-inline:8px}.kanban-plugin__item-form{border-top:1px solid var(--background-modifier-border);padding:8px}.kanban-plugin__item-form .kanban-plugin__item-input-wrapper{padding-block:6px;padding-inline:8px;border:1px solid var(--background-modifier-border);background-color:var(--background-primary);border-radius:var(--input-radius);min-height:var(--input-height)}.kanban-plugin__lane-header-wrapper+.kanban-plugin__item-form{border-top:none;border-bottom:1px solid var(--background-modifier-border);padding-block:4px 8px;padding-inline:8px}.kanban-plugin__item-input-wrapper{--line-height-normal: var(--line-height-tight);display:flex;flex-direction:column;flex-grow:1}.kanban-plugin button.kanban-plugin__item-submit-button{flex-grow:0;flex-shrink:1;font-size:14px;height:auto;line-height:1;margin-block-start:5px;width:auto}.is-mobile .kanban-plugin button.kanban-plugin__item-submit-button{font-size:12px}.is-mobile .kanban-plugin__lane-form-wrapper{--input-height: auto}.is-mobile .kanban-plugin__lane-form-wrapper button{padding-block:var(--size-4-2)}.is-mobile .kanban-plugin__lane-form-wrapper .markdown-source-view.mod-cm6{font-size:var(--font-ui-medium)}.is-mobile .kanban-plugin .kanban-plugin__lane-input-wrapper button.kanban-plugin__item-submit-button{display:none}button.kanban-plugin__new-item-button{background-color:transparent;color:var(--text-muted)}.kanban-plugin__new-item-button:hover{color:var(--text-on-accent);background-color:var(--interactive-accent-hover)}.kanban-plugin__drag-container>.kanban-plugin__item-wrapper .kanban-plugin__item{border-color:var(--interactive-accent);box-shadow:var(--shadow-s),0 0 0 2px hsla(var(--interactive-accent-hsl),.7)}.kanban-plugin__item{font-size:.875rem;border:1px solid var(--background-modifier-border);border-radius:var(--input-radius);overflow:hidden;transition:.3s opacity cubic-bezier(.25,1,.5,1)}.kanban-plugin__item:has(.markdown-source-view){outline:1px solid var(--background-modifier-border-focus);border-color:var(--background-modifier-border-focus)}.kanban-plugin__item-content-wrapper{background:var(--background-primary)}.kanban-plugin__item-title-wrapper{background:var(--background-primary);display:flex;padding-block:6px;padding-inline:8px}.kanban-plugin__item-title-wrapper:not(:only-child){border-bottom:1px solid var(--background-modifier-border)}.kanban-plugin__item-title{width:100%;line-height:var(--line-height-tight);margin-block-start:1px}.kanban-plugin__meta-value,.kanban-plugin__markdown-preview-wrapper{white-space:pre-wrap;white-space:break-spaces;word-break:break-word;overflow-wrap:anywhere;--font-text-size: .875rem;--line-height-normal: var(--line-height-tight);--p-spacing: var(--size-4-2);--list-indent: 1.75em}.kanban-plugin__meta-value .markdown-preview-view,.kanban-plugin__markdown-preview-wrapper .markdown-preview-view{--file-margins: 0}.kanban-plugin__meta-value.inline,.kanban-plugin__markdown-preview-wrapper.inline{display:inline-block}.kanban-plugin__meta-value .kanban-plugin__markdown-preview-view>*:first-child,.kanban-plugin__markdown-preview-wrapper .kanban-plugin__markdown-preview-view>*:first-child{margin-block-start:0}.kanban-plugin__meta-value .kanban-plugin__markdown-preview-view>*:last-child,.kanban-plugin__markdown-preview-wrapper .kanban-plugin__markdown-preview-view>*:last-child{margin-block-end:0}.kanban-plugin__meta-value .markdown-preview-view,.kanban-plugin__markdown-preview-wrapper .markdown-preview-view{width:unset;height:unset;position:unset;overflow-y:unset;overflow-wrap:unset;color:unset;user-select:unset;-webkit-user-select:unset;white-space:normal}.kanban-plugin__meta-value .markdown-preview-view .markdown-embed,.kanban-plugin__markdown-preview-wrapper .markdown-preview-view .markdown-embed,.kanban-plugin__meta-value .markdown-preview-view blockquote,.kanban-plugin__markdown-preview-wrapper .markdown-preview-view blockquote{padding-inline:var(--size-4-2) 0;padding-block:var(--size-4-1);margin-block-start:var(--p-spacing);margin-block-end:var(--p-spacing)}.kanban-plugin__meta-value .kanban-plugin__markdown-preview-view{display:inline-flex}.kanban-plugin__meta-value .kanban-plugin__markdown-preview-view>div:first-child>*:first-child{margin-block-start:0}.kanban-plugin__meta-value .kanban-plugin__markdown-preview-view>div:last-child>*:last-child{margin-block-end:0}.kanban-plugin__embed-link-wrapper{padding:2px;float:right}.kanban-plugin__item-metadata-wrapper:not(:empty){background-color:var(--background-primary-alt);padding-inline:8px;padding-block:6px}.kanban-plugin__item-metadata:not(:empty){padding-block-start:5px;font-size:12px}.kanban-plugin__item-metadata:not(:empty) .markdown-preview-view{line-height:var(--line-height-tight);font-size:inherit}.kanban-plugin__item-metadata>span{display:block}.kanban-plugin__item-metadata>span.kanban-plugin__item-metadata-date-wrapper{display:inline-block}.kanban-plugin__item-metadata .is-button{cursor:var(--cursor)}.kanban-plugin__item-metadata .is-button:hover{color:var(--text-normal)}.kanban-plugin__item-metadata-date-relative:first-letter{text-transform:uppercase}.kanban-plugin__item-metadata a{text-decoration:none}.kanban-plugin__item-task-inline-metadata-item,.kanban-plugin__item-task-metadata-item{display:inline-flex;margin-block:3px 0;margin-inline:0 6px;gap:4px}.kanban-plugin__item-task-inline-metadata-item{padding-inline:2px;background-color:var(--background-secondary);border-radius:var(--radius-s)}.kanban-plugin__table-cell-wrapper .kanban-plugin__item-task-inline-metadata-item{background-color:unset;padding-inline:unset;border-radius:unset}.kanban-plugin__item-tags:not(:empty){padding-block-start:2px}.kanban-plugin__item-tag{display:inline-block;margin-inline-end:4px}.kanban-plugin__item-tags .kanban-plugin__item-tag{font-size:12px;background-color:var(--tag-background, hsla(var(--interactive-accent-hsl), .1));color:var(--tag-color, var(--text-accent));margin-block:3px 0;margin-inline:0 3px}.kanban-plugin__item-tag.is-search-match,.kanban-plugin__item-tags .kanban-plugin__item-tag.is-search-match{background-color:var(--text-highlight-bg);color:var(--text-normal)}.kanban-plugin__meta-table{width:100%;margin:0;line-height:var(--line-height-tight);font-size:.75rem}.kanban-plugin__meta-table .markdown-preview-view{font-size:.75rem}.kanban-plugin__meta-table .kanban-plugin__item-tags .kanban-plugin__item-tag{position:relative;inset-block-start:-2px;margin-block:0 3px}.kanban-plugin__meta-table td{vertical-align:top;padding-block:3px 0;padding-inline:0;width:10%}.kanban-plugin__meta-table td+td{width:90%}.kanban-plugin__meta-table td:only-child{width:100%}.kanban-plugin__meta-table td.kanban-plugin__meta-key{white-space:nowrap;padding-inline-end:5px;color:var(--text-muted)}.kanban-plugin__meta-table td.kanban-plugin__meta-key.is-search-match>span{background-color:var(--text-highlight-bg);color:var(--text-normal)}.kanban-plugin__meta-value:not(.mod-array){white-space:pre-wrap;display:flex}.kanban-plugin__meta-value>.is-search-match,.kanban-plugin__meta-value.is-search-match{background-color:var(--text-highlight-bg);color:var(--text-normal)}.kanban-plugin__item-prefix-button-wrapper,.kanban-plugin__item-postfix-button-wrapper{display:flex;flex-grow:0;flex-shrink:0;align-self:start}.kanban-plugin__item-prefix-button-wrapper>div,.kanban-plugin__item-postfix-button-wrapper>div{display:flex;flex-direction:column;gap:var(--size-4-1)}.kanban-plugin__item-prefix-button-wrapper{flex-direction:column}.kanban-plugin__item-prefix-button-wrapper .kanban-plugin__item-prefix-button{width:var(--checkbox-size);height:var(--checkbox-size)}.kanban-plugin__item-prefix-button-wrapper .kanban-plugin__item-prefix-button,.kanban-plugin__item-prefix-button-wrapper input[type=checkbox]{margin-block:2px;margin-inline:0px 7px}.kanban-plugin__item-prefix-button-wrapper .kanban-plugin__item-prefix-button+button,.kanban-plugin__item-prefix-button-wrapper input[type=checkbox]+button{margin-block-start:10px}button.kanban-plugin__item-postfix-button{visibility:hidden;opacity:0;transition:.1s opacity;display:flex;align-self:flex-start}button.kanban-plugin__item-postfix-button.is-enabled,.kanban-plugin__item:hover button.kanban-plugin__item-postfix-button{visibility:visible;opacity:1}.kanban-plugin__item-settings-actions{padding:5px;display:flex}.kanban-plugin__item-settings-actions>button{line-height:1;display:flex;align-items:center;justify-content:center;font-size:.75rem;width:100%}.kanban-plugin__lane-action-wrapper button>.kanban-plugin__icon,.kanban-plugin__item-settings-actions button>.kanban-plugin__icon{margin-inline-end:5px}.kanban-plugin__item-settings-actions>button:first-child,.kanban-plugin__lane-action-wrapper>button:first-child{margin-inline-end:2.5px}.kanban-plugin__item-settings-actions>button:last-child,.kanban-plugin__lane-action-wrapper>button:last-child{margin-inline-start:2.5px}.kanban-plugin__archive-lane-button,.kanban-plugin__item-button-archive{color:var(--text-muted);border:1px solid var(--background-modifier-border)}.kanban-plugin__archive-lane-button:hover,.kanban-plugin__item-button-archive:hover{color:var(--text-normal)}.kanban-plugin__item-button-delete{border:1px solid rgba(var(--background-modifier-error-rgb),.15);color:rgba(var(--background-modifier-error-rgb),1)}.kanban-plugin__item-button-delete:hover{background-color:rgba(var(--background-modifier-error-rgb),.2);color:var(--text-error)}.theme-dark .kanban-plugin__item-button-delete{background-color:transparent;border:1px solid rgba(var(--background-modifier-error-rgb),1);color:var(--text-error)}.theme-dark .kanban-plugin__item-button-delete:hover{background-color:rgba(var(--background-modifier-error-rgb),1);color:var(--text-error)}.kanban-plugin__checkbox-wrapper{border-top:1px solid var(--background-modifier-border);border-bottom:1px solid var(--background-modifier-border);padding:10px;margin-block-end:10px;display:flex;align-items:center}.kanban-plugin__checkbox-wrapper .checkbox-container{flex-shrink:0;flex-grow:0;margin-inline-start:15px}.kanban-plugin__checkbox-label{font-size:.8125rem;line-height:var(--line-height-tight)}.kanban-plugin__lane-setting-wrapper>div{border-top:none;border-bottom:none;padding-block:10px;padding-inline:15px;margin-block-end:0}.kanban-plugin__lane-setting-wrapper>div:last-child{border-bottom:1px solid var(--background-modifier-border);margin-block-end:10px}.kanban-plugin__action-confirm-wrapper{border:1px solid rgba(var(--background-modifier-error-rgb),.2);background-color:rgba(var(--background-modifier-error-rgb),.1);border-radius:4px;padding:10px;margin-block:5px;margin-inline:10px}.theme-dark .kanban-plugin__action-confirm-wrapper{border:1px solid rgba(var(--background-modifier-error-rgb),1)}.kanban-plugin__delete-lane-button,.kanban-plugin__archive-lane-button{display:flex;align-items:center;justify-content:center;font-size:.75rem;width:50%}.kanban-plugin__delete-lane-button{border:1px solid rgba(var(--background-modifier-error-rgb),.15);color:rgba(var(--background-modifier-error-rgb),1)}.kanban-plugin__delete-lane-button:hover{background-color:rgba(var(--background-modifier-error-rgb),.2);color:var(--text-error)}.theme-dark .kanban-plugin__delete-lane-button{background-color:transparent;border:1px solid rgba(var(--background-modifier-error-rgb),1);color:var(--text-error)}.theme-dark .kanban-plugin__delete-lane-button:hover{background-color:rgba(var(--background-modifier-error-rgb),1);color:var(--text-error)}.kanban-plugin__action-confirm-text{font-size:.875rem;color:var(--text-error);margin-block-end:10px;line-height:var(--line-height-tight)}button.kanban-plugin__confirm-action-button{border:1px solid rgba(var(--background-modifier-error-rgb),.2);margin-inline-end:5px;color:var(--text-error)}button.kanban-plugin__confirm-action-button:hover{background-color:rgba(var(--background-modifier-error-rgb),.5)}button.kanban-plugin__cancel-action-button{border:1px solid var(--background-modifier-border)}.modal.kanban-plugin__board-settings-modal{width:var(--modal-width);height:var(--modal-height);max-height:var(--modal-max-height);max-width:var(--modal-max-width);padding:0;display:flex;flex-direction:column}.modal.kanban-plugin__board-settings-modal .modal-content{padding-block:30px;padding-inline:50px;height:100%;overflow-y:auto;overflow-x:hidden;margin:0}.kanban-plugin__board-settings-modal .setting-item{flex-wrap:wrap;justify-content:space-between}.kanban-plugin__board-settings-modal .setting-item-info{max-width:400px;min-width:300px;width:50%}.kanban-plugin__board-settings-modal .setting-item-control{min-width:300px;flex-shrink:0}.kanban-plugin__board-settings-modal .choices{width:100%;text-align:left}.kanban-plugin__board-settings-modal .choices[data-type*=select-one] .choices__inner{background-color:var(--background-primary);border-color:var(--background-modifier-border);padding:0;min-height:0}.kanban-plugin__board-settings-modal .choices[data-type*=select-one] .choices__input{background-color:var(--background-primary);border-bottom-color:var(--background-modifier-border);font-size:14px}.kanban-plugin__board-settings-modal .choices__input{border-radius:0;border-top:none;border-left:none;border-right:none}.kanban-plugin__board-settings-modal .choices__list[role=listbox]{overflow-x:hidden}.kanban-plugin__board-settings-modal .choices__list--single{padding-block:4px;padding-inline:6px 20px}.kanban-plugin__board-settings-modal .is-open .choices__list--dropdown,.kanban-plugin__board-settings-modal .choices__list--dropdown{background-color:var(--background-primary);border-color:var(--background-modifier-border);word-break:normal;max-height:200px;display:flex;flex-direction:column}.kanban-plugin__board-settings-modal .choices__list--dropdown .choices__item--selectable:after{display:none}.kanban-plugin__board-settings-modal .choices__list--dropdown .choices__item--selectable{padding-block:4px;padding-inline:6px}.kanban-plugin__board-settings-modal .choices__list--dropdown .choices__item.is-highlighted{background-color:var(--background-primary-alt)}.kanban-plugin__board-settings-modal .choices__placeholder{opacity:1;color:var(--text-muted)}.kanban-plugin__board-settings-modal .error{border-color:var(--background-modifier-error-hover)!important}.kanban-plugin__date-picker{position:absolute;z-index:var(--layer-popover);--cell-size: 2.4em}.kanban-plugin__date-picker .flatpickr-input{width:0;height:0;opacity:0;border:none;padding:0;display:block;margin-block-end:-1px}.kanban-plugin__date-picker .flatpickr-current-month{color:var(--text-normal);font-weight:600;font-size:inherit;width:100%;position:static;height:auto;display:flex;align-items:center;justify-content:center;padding:0}.kanban-plugin__date-picker .flatpickr-current-month .numInputWrapper span.arrowUp:after{border-bottom-color:var(--text-normal)}.kanban-plugin__date-picker .flatpickr-current-month .numInputWrapper span.arrowDown:after{border-top-color:var(--text-normal)}.flatpickr-months .flatpickr-prev-month svg path,.flatpickr-months .flatpickr-next-month svg path{fill:currentColor}.kanban-plugin__date-picker .flatpickr-calendar{border-radius:var(--radius-m);font-size:13px;overflow:hidden;background-color:var(--background-primary);width:calc(var(--cell-size) * 7 + 8px);box-shadow:0 0 0 1px var(--background-modifier-border),0 15px 25px #0003}.kanban-plugin__date-picker .flatpickr-calendar.inline{top:0}.kanban-plugin__date-picker .flatpickr-months{font-size:13px;padding-block:2px 4px;padding-inline:2px;align-items:center}.kanban-plugin__date-picker .flatpickr-months .flatpickr-current-month input.cur-year,.kanban-plugin__date-picker .flatpickr-months select{border-radius:4px;padding:4px}.kanban-plugin__date-picker .flatpickr-months .numInputWrapper{border-radius:4px}.kanban-plugin__date-picker .flatpickr-months .flatpickr-month{width:100%;height:auto}.kanban-plugin__date-picker .flatpickr-months .flatpickr-prev-month,.kanban-plugin__date-picker .flatpickr-months .flatpickr-next-month{color:var(--text-normal);fill:currentColor;border-radius:4px;display:flex;align-items:center;justify-content:center;line-height:1;height:auto;padding:5px;position:static;flex-shrink:0}.kanban-plugin__date-picker .flatpickr-months .flatpickr-prev-month:hover,.kanban-plugin__date-picker .flatpickr-months .flatpickr-next-month:hover{background-color:var(--background-primary-alt);color:var(--text-normal)}.kanban-plugin__date-picker .flatpickr-months .flatpickr-prev-month:hover svg,.kanban-plugin__date-picker .flatpickr-months .flatpickr-next-month:hover svg{fill:currentColor}.kanban-plugin__date-picker .flatpickr-current-month .flatpickr-monthDropdown-months{box-shadow:none;color:var(--text-normal);font-weight:inherit;margin-inline-end:5px}.kanban-plugin__date-picker .flatpickr-current-month input.cur-year{color:var(--text-normal);font-weight:inherit}.kanban-plugin__date-picker .flatpickr-weekdays{height:auto;padding-block:8px 12px;padding-inline:0}.kanban-plugin__date-picker span.flatpickr-weekday{font-weight:400;color:var(--text-muted)}.kanban-plugin__date-picker .flatpickr-innerContainer{padding:4px}.kanban-plugin__date-picker .flatpickr-day{color:var(--text-normal);display:inline-flex;align-items:center;justify-content:center;width:var(--cell-size);height:var(--cell-size);line-height:1;border-radius:6px}.kanban-plugin__date-picker .flatpickr-day.today{border-color:var(--interactive-accent)}.kanban-plugin__date-picker .flatpickr-day.today:hover{color:var(--text-normal);border-color:var(--interactive-accent);background-color:var(--background-primary-alt)}.kanban-plugin__date-picker .flatpickr-day.selected{border-color:var(--interactive-accent);background-color:var(--interactive-accent);color:var(--text-on-accent)}.kanban-plugin__date-picker .flatpickr-day.selected:hover{border-color:var(--interactive-accent);background-color:var(--interactive-accent)}.kanban-plugin__date-picker .flatpickr-days{width:calc(var(--cell-size) * 7)}.kanban-plugin__date-picker .dayContainer{width:calc(var(--cell-size) * 7);min-width:calc(var(--cell-size) * 7);max-width:calc(var(--cell-size) * 7)}.kanban-plugin__date-picker .flatpickr-day.inRange,.kanban-plugin__date-picker .flatpickr-day.prevMonthDay.inRange,.kanban-plugin__date-picker .flatpickr-day.nextMonthDay.inRange,.kanban-plugin__date-picker .flatpickr-day.today.inRange,.kanban-plugin__date-picker .flatpickr-day.prevMonthDay.today.inRange,.kanban-plugin__date-picker .flatpickr-day.nextMonthDay.today.inRange,.kanban-plugin__date-picker .flatpickr-day:hover,.kanban-plugin__date-picker .flatpickr-day.prevMonthDay:hover,.kanban-plugin__date-picker .flatpickr-day.nextMonthDay:hover,.kanban-plugin__date-picker .flatpickr-day:focus,.kanban-plugin__date-picker .flatpickr-day.prevMonthDay:focus,.kanban-plugin__date-picker .flatpickr-day.nextMonthDay:focus{background-color:var(--background-primary-alt);border-color:var(--background-primary-alt)}.kanban-plugin__date-picker .flatpickr-day.flatpickr-disabled,.kanban-plugin__date-picker .flatpickr-day.flatpickr-disabled:hover,.kanban-plugin__date-picker .flatpickr-day.prevMonthDay,.kanban-plugin__date-picker .flatpickr-day.nextMonthDay,.kanban-plugin__date-picker .flatpickr-day.notAllowed,.kanban-plugin__date-picker .flatpickr-day.notAllowed.prevMonthDay,.kanban-plugin__date-picker .flatpickr-day.notAllowed.nextMonthDay{color:var(--text-faint)}.kanban-plugin__time-picker{position:absolute;max-height:250px;overflow:auto;border-radius:4px;border:1px solid var(--background-modifier-border);box-shadow:0 2px 8px var(--background-modifier-box-shadow);background:var(--background-primary);color:var(--text-normal);font-size:14px;z-index:var(--layer-menu)}.kanban-plugin__time-picker-item{display:flex;align-items:center;color:var(--text-muted);cursor:var(--cursor);line-height:1;padding-block:6px;padding-inline:8px}.kanban-plugin__time-picker-check{visibility:hidden;display:inline-flex;margin-inline-end:5px}.kanban-plugin__time-picker-item.is-hour{color:var(--text-normal);font-weight:600}.kanban-plugin__time-picker-item.is-selected .kanban-plugin__time-picker-check{visibility:visible}.kanban-plugin__time-picker-item:hover,.kanban-plugin__time-picker-item.is-selected{background:var(--background-secondary)}.kanban-plugin mark{background-color:var(--text-highlight-bg)}.kanban-plugin__draggable-setting-container{border-top:0;padding:0;flex-direction:column}.kanban-plugin__draggable-setting-container>div{width:100%;margin-inline-end:0!important}.kanban-plugin__setting-item-wrapper{border-top:1px solid var(--background-modifier-border)}.kanban-plugin__draggable-setting-container>.kanban-plugin__placeholder{border-top:1px solid var(--background-modifier-border)}.kanban-plugin__setting-item{background-color:var(--background-secondary);width:100%;font-size:16px;display:flex;align-items:flex-start;padding:12px;color:var(--text-muted)}.kanban-plugin__drag-container .kanban-plugin__setting-item{border:1px solid hsla(var(--interactive-accent-hsl),.8);box-shadow:0 15px 25px #0003,0 0 0 2px hsla(var(--interactive-accent-hsl),.8)}.kanban-plugin__setting-controls-wrapper{flex-grow:1;flex-shrink:1}.kanban-plugin__setting-input-wrapper{display:flex;flex-wrap:wrap;margin-block-end:1rem}.kanban-plugin__setting-input-wrapper>div{margin-inline-end:10px}.kanban-plugin__setting-toggle-wrapper>div{display:flex;align-items:center;line-height:1;margin-block-end:10px}.kanban-plugin__setting-toggle-wrapper .checkbox-container{margin-inline-end:10px}.kanban-plugin__setting-button-wrapper{display:flex;justify-content:flex-end;flex-grow:1;flex-shrink:0;max-width:25px}.kanban-plugin__setting-button-wrapper>div{margin-inline-start:12px}.kanban-plugin__setting-key-input-wrapper{margin-block:1rem;margin-inline:0}.kanban-plugin__setting-key-input-wrapper>input{margin-inline-end:10px}.kanban-plugin__date-color-input-wrapper,.kanban-plugin__tag-sort-input-wrapper,.kanban-plugin__tag-color-input-wrapper{display:flex;flex-direction:column;flex-grow:1;gap:1rem}.kanban-plugin__tag-sort-input-wrapper .kanban-plugin__setting-key-input-wrapper{margin-block-start:0}.kanban-plugin__tag-sort-input-wrapper .kanban-plugin__setting-input-wrapper{margin:0}.kanban-plugin__add-tag-color-button{align-self:baseline;margin:0}.kanban-plugin__date-color-wrapper,.kanban-plugin__tag-color-input .kanban-plugin__item-tags{background-color:var(--background-primary);padding:10px;margin:0;border-radius:4px}.kanban-plugin__tag-color-input .kanban-plugin__item-tag{margin-block-start:0;font-size:13px;font-weight:500;line-height:1.5}.kanban-plugin__date-color-input-wrapper input[type=number]{width:75px;padding-block:.6em;padding-inline:.8em;height:auto;border-radius:.5em}.kanban-plugin__date-color-input-wrapper .kanban-plugin__setting-item-label{margin-block-end:0}.kanban-plugin__date-color-config{padding-block:0 10px;padding-inline:0;display:flex;flex-wrap:wrap;gap:5px;align-items:center}.kanban-plugin__date-color-wrapper{display:inline-block;margin-block-start:10px}.kanban-plugin__date-color-wrapper .kanban-plugin__item-metadata{padding:0}.kanban-plugin__metadata-setting-desc{font-size:14px}.kanban-plugin__setting-button-spacer{visibility:hidden}.kanban-plugin__setting-item-label{font-size:12px;font-weight:700;margin-block-end:5px}.kanban-plugin__setting-toggle-wrapper .kanban-plugin__setting-item-label{margin-block-end:0}.kanban-plugin__hitbox{border:2px dashed tomato}.kanban-plugin__placeholder{flex-grow:0;flex-shrink:0;width:0;height:0;pointer-events:none}.kanban-plugin__placeholder[data-axis=horizontal]{height:100%}.kanban-plugin__placeholder[data-axis=vertical]{width:100%}body:not(.native-scrollbars) .kanban-plugin__scroll-container::-webkit-scrollbar{background-color:transparent;width:16px;height:16px}body:not(.native-scrollbars) .kanban-plugin__scroll-container::-webkit-scrollbar-thumb{border:4px solid transparent;background-clip:content-box}.kanban-plugin__scroll-container{will-change:transform}.kanban-plugin__scroll-container.kanban-plugin__horizontal{overflow-y:hidden;overflow-x:auto}.kanban-plugin__scroll-container.kanban-plugin__vertical{overflow-y:auto;overflow-x:hidden}.kanban-plugin__drag-container{contain:layout size;z-index:10000;pointer-events:none;position:fixed;top:0;left:0}.kanban-plugin__loading{width:100%;height:100%;display:flex;justify-content:center;align-items:center}.sk-pulse{width:60px;height:60px;background-color:var(--text-faint);border-radius:100%;animation:sk-pulse 1.2s infinite cubic-bezier(.455,.03,.515,.955)}@keyframes sk-pulse{0%{transform:scale(0)}to{transform:scale(1);opacity:0}}.kanban-plugin__color-picker-wrapper{position:relative}.kanban-plugin__color-picker{position:absolute;top:-5px;left:0;transform:translateY(-100%)}.kanban-plugin__date,.cm-kanban-time-wrapper,.cm-kanban-date-wrapper{display:inline-block;color:var(--date-color);border-radius:var(--radius-s);background-color:var(--date-background-color, rgba(var(--mono-rgb-100), .05))}.kanban-plugin__date:hover,.cm-kanban-time-wrapper:hover,.cm-kanban-date-wrapper:hover{background-color:var(--date-background-color, rgba(var(--mono-rgb-100), .1))}.kanban-plugin__date.kanban-plugin__preview-date-link,.cm-kanban-time-wrapper.kanban-plugin__preview-date-link,.cm-kanban-date-wrapper.kanban-plugin__preview-date-link{--link-decoration: none;--link-unresolved-decoration-style: unset}.kanban-plugin__date>span,.cm-kanban-time-wrapper>span,.cm-kanban-date-wrapper>span,.kanban-plugin__date>a,.cm-kanban-time-wrapper>a,.cm-kanban-date-wrapper>a{padding-inline:var(--size-2-1)}.completion .kanban-plugin__date.has-background{color:inherit;background-color:transparent}.completion .kanban-plugin__date.has-background:hover{background-color:transparent}.is-date .kanban-plugin__date:not(.has-background){background-color:transparent}.is-date .kanban-plugin__date:not(.has-background):hover{background-color:transparent}.kanban-plugin__meta-value .kanban-plugin__date:hover{background-color:var(--date-background-color, rgba(var(--mono-rgb-100), .05))}
 /* set the styles */

.password-second-confirm {
  font-weight: bold;
}

.password-disclaimer {
  font-weight: bold;
}

.encryptionmethod-second-confirm {
  font-weight: bold;
}

.settings-auth-related {
  border-top: 1px solid var(--background-modifier-border);
  padding-top: 18px;
}

.settings-percentage-custom-hide {
  display: none;
}

.settings-encryption-method-hide {
  display: none;
}

.s3-disclaimer {
  font-weight: bold;
}
.s3-hide {
  display: none;
}

.dropbox-disclaimer {
  font-weight: bold;
}
.dropbox-hide {
  display: none;
}

.dropbox-auth-button-hide {
  display: none;
}

.dropbox-revoke-auth-button-hide {
  display: none;
}

.onedrive-disclaimer {
  font-weight: bold;
}
.onedrive-hide {
  display: none;
}

.onedrive-auth-button-hide {
  display: none;
}

.onedrive-revoke-auth-button-hide {
  display: none;
}

.onedrivefull-allow-to-use-hide {
  display: none;
}

.onedrivefull-disclaimer {
  font-weight: bold;
}
.onedrivefull-hide {
  display: none;
}

.onedrivefull-auth-button-hide {
  display: none;
}

.onedrivefull-revoke-auth-button-hide {
  display: none;
}

.webdav-disclaimer {
  font-weight: bold;
}
.webdav-hide {
  display: none;
}

.webdav-customheaders-textarea {
  font-family: monospace;
}

.webdis-disclaimer {
  font-weight: bold;
}
.webdis-hide {
  display: none;
}

.googledrive-disclaimer {
  font-weight: bold;
}
.googledrive-hide {
  display: none;
}

.googledrive-allow-to-use-hide {
  display: none;
}

.googledrive-auth-button-hide {
  display: none;
}

.googledrive-revoke-auth-button-hide {
  display: none;
}

.box-disclaimer {
  font-weight: bold;
}
.box-hide {
  display: none;
}

.box-allow-to-use-hide {
  display: none;
}

.box-auth-button-hide {
  display: none;
}

.box-revoke-auth-button-hide {
  display: none;
}

.pcloud-disclaimer {
  font-weight: bold;
}
.pcloud-hide {
  display: none;
}

.pcloud-allow-to-use-hide {
  display: none;
}

.pcloud-auth-button-hide {
  display: none;
}

.pcloud-revoke-auth-button-hide {
  display: none;
}

.yandexdisk-disclaimer {
  font-weight: bold;
}
.yandexdisk-hide {
  display: none;
}

.yandexdisk-allow-to-use-hide {
  display: none;
}

.yandexdisk-auth-button-hide {
  display: none;
}

.yandexdisk-revoke-auth-button-hide {
  display: none;
}

.koofr-disclaimer {
  font-weight: bold;
}
.koofr-hide {
  display: none;
}

.koofr-allow-to-use-hide {
  display: none;
}

.koofr-auth-button-hide {
  display: none;
}

.koofr-revoke-auth-button-hide {
  display: none;
}

.azureblobstorage-disclaimer {
  font-weight: bold;
}
.azureblobstorage-hide {
  display: none;
}

.azureblobstorage-allow-to-use-hide {
  display: none;
}

.qrcode-img {
  width: 350px;
  height: 350px;
}

.ignorepaths-textarea {
  font-family: monospace;
}

.onlyallowpaths-textarea {
  font-family: monospace;
}

.logtohttpserver-warning {
  color: red;
  font-weight: bolder;
}

.setting-need-wrapping .setting-item-control {
  /* flex-wrap: wrap; */
  display: grid;
}

.pro-disclaimer {
  font-weight: bold;
}
.pro-hide {
  display: none;
}

.pro-auth-button-hide {
  display: none;
}

.pro-revoke-auth-button-hide {
  display: none;
}
 
.pandoc-plugin-error {
    color: red;
}
 .omnisearch-modal {
}

.omnisearch-result {
  white-space: normal;
  display: flex;
  flex-direction: row;
  /* justify-content: space-between; */
  flex-wrap: nowrap;
}

.omnisearch-result__title-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  column-gap: 5px;
  flex-wrap: wrap;
}

.omnisearch-result__title {
  white-space: pre-wrap;
  align-items: center;
  display: flex;
  gap: 5px;
}

.omnisearch-result__title > span {
}

.omnisearch-result__folder-path {
  font-size: 0.75rem;
  align-items: center;
  display: flex;
  gap: 5px;
  color: var(--text-muted);
}

.omnisearch-result__extension {
  font-size: 0.7rem;
  color: var(--text-muted);
}

.omnisearch-result__counter {
  font-size: 0.7rem;
  color: var(--text-muted);
}

.omnisearch-result__body {
  white-space: normal;
  font-size: small;
  word-wrap: normal;

  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;

  color: var(--text-muted);
  margin-inline-start: 0.5em;
}

.omnisearch-result__embed {
  margin-left: 1em;
}


.omnisearch-result__image-container {
  flex-basis: 20%;
  text-align: end;
}

.omnisearch-highlight {
}

.omnisearch-default-highlight {
  text-decoration: underline;
  text-decoration-color: var(--text-highlight-bg);
  text-decoration-thickness: 3px;
  text-underline-offset: -1px;
  text-decoration-skip-ink: none;
}

.omnisearch-input-container {
  display: flex;
  align-items: center;
  flex-direction: row;
  gap: 5px;
}

.omnisearch-result__icon {
  display: inline-block;
  vertical-align: middle;
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.omnisearch-result__icon svg {
  width: 100%;
  height: 100%;
}

.omnisearch-result__icon--emoji {
  font-size: 16px;
  vertical-align: middle;
  margin-right: 4px;
}

@media only screen and (max-width: 600px) {
  .omnisearch-input-container {
    flex-direction: column;
  }

  .omnisearch-input-container__buttons {
    display: flex;
    flex-direction: row;
    width: 100%;
    padding: 0 1em 0 1em;
    gap: 1em;
  }
  .omnisearch-input-container__buttons > button {
    flex-grow: 1;
  }
}

@media only screen and (min-width: 600px) {
  .omnisearch-input-container__buttons {
    margin-inline-end: 1em;
  }
}

.omnisearch-input-field {
  position: relative;
  flex-grow: 1;
}
  .commit-msg-input.svelte-11adhly {width:100%;overflow:hidden;resize:none;padding:7px 5px;background-color:var(--background-modifier-form-field);}.git-commit-msg.svelte-11adhly {position:relative;padding:0;width:calc(100% - var(--size-4-8));margin:4px auto;}main.svelte-11adhly .git-tools:where(.svelte-11adhly) .files-count:where(.svelte-11adhly) {padding-left:var(--size-2-1);width:11px;display:flex;align-items:center;justify-content:center;}.nav-folder-title.svelte-11adhly {align-items:center;}.git-commit-msg-clear-button.svelte-11adhly {position:absolute;background:transparent;border-radius:50%;color:var(--search-clear-button-color);cursor:var(--cursor);top:-4px;right:2px;bottom:0px;line-height:0;height:var(--input-height);width:28px;margin:auto;padding:0 0;text-align:center;display:flex;justify-content:center;align-items:center;transition:color 0.15s ease-in-out;}.git-commit-msg-clear-button.svelte-11adhly:after {content:"";height:var(--search-clear-button-size);width:var(--search-clear-button-size);display:block;background-color:currentColor;mask-image:url("data:image/svg+xml,<svg viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M6 12C9.31371 12 12 9.31371 12 6C12 2.68629 9.31371 0 6 0C2.68629 0 0 2.68629 0 6C0 9.31371 2.68629 12 6 12ZM3.8705 3.09766L6.00003 5.22718L8.12955 3.09766L8.9024 3.8705L6.77287 6.00003L8.9024 8.12955L8.12955 8.9024L6.00003 6.77287L3.8705 8.9024L3.09766 8.12955L5.22718 6.00003L3.09766 3.8705L3.8705 3.09766Z' fill='currentColor'/></svg>");mask-repeat:no-repeat;-webkit-mask-image:url("data:image/svg+xml,<svg viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M6 12C9.31371 12 12 9.31371 12 6C12 2.68629 9.31371 0 6 0C2.68629 0 0 2.68629 0 6C0 9.31371 2.68629 12 6 12ZM3.8705 3.09766L6.00003 5.22718L8.12955 3.09766L8.9024 3.8705L6.77287 6.00003L8.9024 8.12955L8.12955 8.9024L6.00003 6.77287L3.8705 8.9024L3.09766 8.12955L5.22718 6.00003L3.09766 3.8705L3.8705 3.09766Z' fill='currentColor'/></svg>");-webkit-mask-repeat:no-repeat;}undefined
</style>
    </head>
    <body>
<h1 data-heading="SERVICE LEVEL AGREEMENT (SLA)" dir="auto">SERVICE LEVEL AGREEMENT (SLA)</h1>
<h2 data-heading="CI/CD PIPELINE MANAGEMENT AND WEBSITE HOSTING SERVICES" dir="auto">CI/CD PIPELINE MANAGEMENT AND WEBSITE HOSTING SERVICES</h2>
<p dir="auto"><strong>BETWEEN:</strong></p>
<p dir="auto"><strong>PaceySpace Digital</strong> ("Service Provider")<br>
ABN: [Insert ABN]<br>
Address: Caboolture, Queensland, Australia 4510<br>
Email: <a data-tooltip-position="top" aria-label="mailto:<EMAIL>" rel="noopener nofollow" class="external-link" href="mailto:<EMAIL>" target="_blank"><EMAIL></a><br>
Phone: 07 2111 0402<br>
GST Registered: Yes</p>
<p dir="auto"><strong>AND</strong></p>
<p dir="auto"><strong>[Client Name]</strong> ("Client")<br>
ABN: [Insert Client ABN]<br>
Address: [Insert Client Address]<br>
Email: [Insert Client Email]<br>
Phone: [Insert Client Phone]</p>
<h2 data-heading="1. SERVICE OVERVIEW" dir="auto">1. SERVICE OVERVIEW</h2>
<p dir="auto">This Service Level Agreement (SLA) defines the terms and conditions under which PaceySpace Digital will provide CI/CD pipeline management and website hosting services for the YendorCats website using the Enhance Control Panel and associated infrastructure. This agreement is made in Queensland, Australia and is subject to Australian law, including the Australian Consumer Law.</p>
<h2 data-heading="2. TERM" dir="auto">2. TERM</h2>
<p dir="auto">2.1. This SLA shall commence on [Start Date] and continue for a period of twelve (12) months ("Initial Term").</p>
<p dir="auto">2.2. This SLA shall automatically renew for successive twelve (12) month periods ("Renewal Term") unless either party provides written notice of non-renewal at least sixty (60) days prior to the end of the current Term.</p>
<h2 data-heading="3. SERVICES PROVIDED" dir="auto">3. SERVICES PROVIDED</h2>
<h3 data-heading="3.1. CI/CD Pipeline Management" dir="auto">3.1. CI/CD Pipeline Management</h3>
<p dir="auto">The Service Provider shall:</p>
<p dir="auto">a) Implement and maintain a continuous integration and continuous deployment (CI/CD) pipeline for the YendorCats website using the Enhance Control Panel.</p>
<p dir="auto">b) Configure automated testing, build, and deployment processes.</p>
<p dir="auto">c) Monitor the CI/CD pipeline for failures and resolve issues promptly.</p>
<p dir="auto">d) Maintain version control and code quality assurance.</p>
<p dir="auto">e) Provide documentation of the CI/CD pipeline configuration.</p>
<p dir="auto">f) Implement security best practices within the CI/CD pipeline.</p>
<p dir="auto">g) Complete initial CI/CD pipeline setup within [X] days of the commencement date, with a 2-week leeway period for unforeseen circumstances.</p>
<h3 data-heading="3.2. Website Hosting Services" dir="auto">3.2. Website Hosting Services</h3>
<p dir="auto">The Service Provider shall:</p>
<p dir="auto">a) Provision and maintain appropriate infrastructure for hosting the YendorCats website, including:</p>
<ul>
<li dir="auto">Docker containers for all services (API, Database, File Uploader)</li>
<li dir="auto">MariaDB database for data storage</li>
<li dir="auto">Backblaze B2 for S3-compatible storage</li>
<li dir="auto">Cloudflare integration for CDN, WAF, and DNS services</li>
</ul>
<p dir="auto">b) Configure and maintain HTTPS for secure communication.</p>
<p dir="auto">c) Implement and maintain backup systems for website data.</p>
<p dir="auto">d) Monitor website performance and availability.</p>
<p dir="auto">e) Apply security patches and updates to all infrastructure components.</p>
<p dir="auto">f) Provide DNS management services.</p>
<p dir="auto">g) Complete initial hosting infrastructure setup within [X] days of the commencement date, with a 2-week leeway period for unforeseen circumstances.</p>
<h3 data-heading="3.3. Scalability Services" dir="auto">3.3. Scalability Services</h3>
<p dir="auto">The Service Provider shall:</p>
<p dir="auto">a) Implement infrastructure capable of handling traffic spikes during cat breeding times and major announcements.</p>
<p dir="auto">b) Configure auto-scaling capabilities where appropriate.</p>
<p dir="auto">c) Provision replicated instances as needed to maintain performance during high-traffic periods.</p>
<p dir="auto">d) Monitor resource utilization and scale resources proactively.</p>
<p dir="auto">e) Implement load balancing for distributed traffic handling.</p>
<p dir="auto">f) Optimize database performance for high-traffic scenarios.</p>
<p dir="auto">g) Implement scalability features within [X] days of the commencement date, with a 2-week leeway period for unforeseen circumstances.</p>
<p dir="auto">h) Scale resources within 24 hours of receiving notice from the Client about anticipated traffic spikes, with a 2-week advance notice requirement from the Client for major events.</p>
<h2 data-heading="4. SERVICE LEVELS" dir="auto">4. SERVICE LEVELS</h2>
<h3 data-heading="4.1. Website Uptime" dir="auto">4.1. Website Uptime</h3>
<p dir="auto">a) The Service Provider guarantees 99.9% uptime for the YendorCats website, measured monthly.</p>
<p dir="auto">b) Uptime excludes scheduled maintenance windows, which shall be communicated to the Client at least 48 hours in advance.</p>
<p dir="auto">c) Scheduled maintenance shall be performed during off-peak hours (typically between 11:00 PM and 5:00 AM AEST) unless otherwise agreed.</p>
<h3 data-heading="4.2. Performance Metrics" dir="auto">4.2. Performance Metrics</h3>
<p dir="auto">a) Page load time: The website shall load within 3 seconds for 90% of page requests under normal traffic conditions.</p>
<p dir="auto">b) API response time: API endpoints shall respond within 500ms for 95% of requests under normal traffic conditions.</p>
<p dir="auto">c) Database query performance: 95% of database queries shall complete within 200ms.</p>
<h3 data-heading="4.3. Scalability Metrics" dir="auto">4.3. Scalability Metrics</h3>
<p dir="auto">a) The infrastructure shall be capable of handling a minimum of 500 concurrent users during normal operations.</p>
<p dir="auto">b) During peak periods (cat breeding times and major announcements), the infrastructure shall scale to handle up to 2,000 concurrent users without significant performance degradation.</p>
<p dir="auto">c) Auto-scaling shall initiate when resource utilization exceeds 70% for more than 5 minutes.</p>
<h3 data-heading="4.4. Backup and Recovery" dir="auto">4.4. Backup and Recovery</h3>
<p dir="auto">a) Database backups shall be performed daily with a retention period of 30 days.</p>
<p dir="auto">b) File storage backups shall be performed weekly with a retention period of 90 days.</p>
<p dir="auto">c) Recovery Point Objective (RPO): Maximum data loss in the event of a disaster shall not exceed 24 hours.</p>
<p dir="auto">d) Recovery Time Objective (RTO): In the event of a disaster, services shall be restored within 4 hours of the incident being reported.</p>
<h2 data-heading="5. SUPPORT AND INCIDENT RESPONSE" dir="auto">5. SUPPORT AND INCIDENT RESPONSE</h2>
<h3 data-heading="5.1. Support Hours" dir="auto">5.1. Support Hours</h3>
<p dir="auto">a) Standard support hours: Monday to Friday, 9:00 AM to 5:00 PM AEST, excluding public holidays in Queensland, Australia.</p>
<p dir="auto">b) After-hours support: Available for critical incidents only.</p>
<h3 data-heading="5.2. Incident Classification and Response Times" dir="auto">5.2. Incident Classification and Response Times</h3>
<table>
<thead>
<tr>
<th>Severity</th>
<th>Description</th>
<th>Response Time</th>
<th>Resolution Time</th>
</tr>
</thead>
<tbody>
<tr>
<td>Critical</td>
<td>Complete service outage or security breach</td>
<td>30 minutes</td>
<td>4 hours</td>
</tr>
<tr>
<td>High</td>
<td>Partial service outage or severe performance degradation</td>
<td>2 hours</td>
<td>8 hours</td>
</tr>
<tr>
<td>Medium</td>
<td>Non-critical feature unavailable or minor performance issues</td>
<td>4 hours</td>
<td>24 hours</td>
</tr>
<tr>
<td>Low</td>
<td>Cosmetic issues or feature requests</td>
<td>8 hours</td>
<td>72 hours</td>
</tr>
</tbody>
</table>
<h3 data-heading="5.3. Incident Reporting" dir="auto">5.3. Incident Reporting</h3>
<p dir="auto">a) The Client shall report incidents via:</p>
<ul>
<li dir="auto">Email: <a data-tooltip-position="top" aria-label="mailto:<EMAIL>" rel="noopener nofollow" class="external-link" href="mailto:<EMAIL>" target="_blank"><EMAIL></a></li>
<li dir="auto">Phone: 07 2111 0402</li>
<li dir="auto">Support portal: [Insert Support Portal URL]</li>
</ul>
<p dir="auto">b) Each incident report shall include:</p>
<ul>
<li dir="auto">Description of the issue</li>
<li dir="auto">Time the issue was first observed</li>
<li dir="auto">Steps to reproduce (if applicable)</li>
<li dir="auto">Impact on business operations</li>
</ul>
<h3 data-heading="5.4. Incident Management Process" dir="auto">5.4. Incident Management Process</h3>
<p dir="auto">a) Acknowledgment: The Service Provider shall acknowledge receipt of all incident reports within the specified response time.</p>
<p dir="auto">b) Investigation: The Service Provider shall investigate the root cause of the incident.</p>
<p dir="auto">c) Resolution: The Service Provider shall implement a solution to resolve the incident within the resolution times specified in Section 5.2, with a 2-week leeway period for complex issues that require third-party intervention or extensive development work.</p>
<p dir="auto">d) Communication: The Service Provider shall provide regular updates on the status of critical and high-severity incidents at intervals not exceeding 4 hours for critical incidents and 8 hours for high-severity incidents.</p>
<p dir="auto">e) Post-incident review: For critical incidents, the Service Provider shall provide a post-incident report within 48 hours of resolution.</p>
<h2 data-heading="6. MONITORING AND REPORTING" dir="auto">6. MONITORING AND REPORTING</h2>
<h3 data-heading="6.1. Monitoring" dir="auto">6.1. Monitoring</h3>
<p dir="auto">a) The Service Provider shall implement comprehensive monitoring of all infrastructure components, including:</p>
<ul>
<li dir="auto">Server health and resource utilization</li>
<li dir="auto">Database performance</li>
<li dir="auto">Website availability and performance</li>
<li dir="auto">CI/CD pipeline status</li>
<li dir="auto">Security events</li>
</ul>
<p dir="auto">b) Automated alerts shall be configured for potential issues.</p>
<h3 data-heading="6.2. Regular Reporting" dir="auto">6.2. Regular Reporting</h3>
<p dir="auto">a) The Service Provider shall provide monthly performance reports including:</p>
<ul>
<li dir="auto">Website uptime statistics</li>
<li dir="auto">Performance metrics</li>
<li dir="auto">Incident summary</li>
<li dir="auto">Backup status</li>
<li dir="auto">Security status</li>
<li dir="auto">Resource utilization trends</li>
</ul>
<p dir="auto">b) Monthly reports shall be delivered within 10 business days after the end of each month, with a 2-week leeway period for complex reporting requirements.</p>
<p dir="auto">c) Quarterly service review meetings shall be scheduled to discuss performance, issues, and improvement opportunities. These meetings shall be scheduled at least 2 weeks in advance.</p>
<h2 data-heading="7. FEES AND PAYMENT" dir="auto">7. FEES AND PAYMENT</h2>
<h3 data-heading="7.1. Service Fees" dir="auto">7.1. Service Fees</h3>
<p dir="auto">a) Base monthly fee: $[Amount] per month for the services described in Section 3.</p>
<p dir="auto">b) All fees are in Australian Dollars (AUD) and include Goods and Services Tax (GST) unless otherwise specified.</p>
<p dir="auto">c) Additional fees for resource scaling during high-traffic periods shall be calculated based on actual usage according to the following rates:</p>
<ul>
<li dir="auto">Additional container instances: $[Amount] per instance per day</li>
<li dir="auto">Additional database resources: $[Amount] per GB of storage per month</li>
<li dir="auto">Additional bandwidth: $[Amount] per GB beyond the included allocation</li>
<li dir="auto">Additional S3 storage: $[Amount] per GB per month</li>
</ul>
<p dir="auto">d) The Service Provider will provide a tax invoice compliant with Australian GST requirements for all charges.</p>
<h3 data-heading="7.2. Payment Terms" dir="auto">7.2. Payment Terms</h3>
<p dir="auto">a) The Client shall be invoiced monthly in advance for the base fee.</p>
<p dir="auto">b) Additional usage fees shall be invoiced monthly in arrears.</p>
<p dir="auto">c) All invoices are due within fourteen (14) days of the invoice date.</p>
<p dir="auto">d) Late payments are subject to a grace period of one (1) month from the due date.</p>
<p dir="auto">e) For annual contract terms, a grace period of three (3) months is provided, in accordance with Australian Consumer Law.</p>
<p dir="auto">f) If payment is not received within the applicable grace period, the Service Provider reserves the right to suspend services with 48 hours written notice.</p>
<p dir="auto">g) The Service Provider will provide a 2-week leeway period before applying late payment fees, which will be charged at [X]% per month on the outstanding amount.</p>
<h2 data-heading="8. SERVICE CREDITS" dir="auto">8. SERVICE CREDITS</h2>
<h3 data-heading="8.1. Service Credit Calculation" dir="auto">8.1. Service Credit Calculation</h3>
<p dir="auto">a) If the Service Provider fails to meet the uptime guarantee specified in Section 4.1, the Client shall be entitled to service credits according to the following schedule:</p>
<table>
<thead>
<tr>
<th>Monthly Uptime</th>
<th>Service Credit (% of monthly fee)</th>
</tr>
</thead>
<tbody>
<tr>
<td>99.0% - 99.9%</td>
<td>10%</td>
</tr>
<tr>
<td>98.0% - 98.9%</td>
<td>25%</td>
</tr>
<tr>
<td>97.0% - 97.9%</td>
<td>50%</td>
</tr>
<tr>
<td>Below 97.0%</td>
<td>100%</td>
</tr>
</tbody>
</table>
<p dir="auto">b) Service credits shall be applied to the next monthly invoice.</p>
<h3 data-heading="8.2. Service Credit Request Process" dir="auto">8.2. Service Credit Request Process</h3>
<p dir="auto">a) The Client must request service credits in writing within 30 days of the end of the month in which the service level failure occurred.</p>
<p dir="auto">b) Service credit requests shall include:</p>
<ul>
<li dir="auto">Date and time of the service disruption</li>
<li dir="auto">Description of the service disruption</li>
<li dir="auto">Impact on the Client's operations</li>
</ul>
<p dir="auto">c) The Service Provider shall review all service credit requests and respond within 14 days.</p>
<h2 data-heading="9. CLIENT RESPONSIBILITIES" dir="auto">9. CLIENT RESPONSIBILITIES</h2>
<p dir="auto">The Client shall:</p>
<p dir="auto">a) Provide timely access to necessary information, systems, and personnel.</p>
<p dir="auto">b) Designate a primary point of contact for communication with the Service Provider.</p>
<p dir="auto">c) Promptly report any issues or incidents.</p>
<p dir="auto">d) Pay all invoices in accordance with the payment terms.</p>
<p dir="auto">e) Provide reasonable notice of anticipated traffic spikes or special events, with at least 2 weeks advance notice for major events.</p>
<p dir="auto">f) Comply with all applicable laws and regulations regarding website content and data.</p>
<p dir="auto">g) Maintain control over website content and ensure it does not violate any laws or third-party rights.</p>
<p dir="auto">h) Respond to Service Provider requests for information or approvals within 5 business days, with a 2-week leeway period for complex requests.</p>
<p dir="auto">i) Provide feedback on deliverables within 10 business days, with a 2-week leeway period for complex deliverables.</p>
<h2 data-heading="10. CONFIDENTIALITY" dir="auto">10. CONFIDENTIALITY</h2>
<p dir="auto">10.1. Each party shall maintain the confidentiality of all confidential information disclosed by the other party.</p>
<p dir="auto">10.2. Confidential information includes, but is not limited to, business plans, customer data, financial information, and technical specifications.</p>
<p dir="auto">10.3. This obligation of confidentiality shall survive the termination of this SLA.</p>
<h2 data-heading="11. LIMITATION OF LIABILITY" dir="auto">11. LIMITATION OF LIABILITY</h2>
<p dir="auto">11.1. The Service Provider's total liability under this SLA shall not exceed the total fees paid by the Client in the twelve (12) months preceding the event giving rise to the liability.</p>
<p dir="auto">11.2. Neither party shall be liable for any indirect, special, incidental, or consequential damages.</p>
<h2 data-heading="12. TERMINATION" dir="auto">12. TERMINATION</h2>
<p dir="auto">12.1. Either party may terminate this SLA for cause if the other party materially breaches this SLA and fails to cure such breach within thirty (30) days of receiving written notice.</p>
<p dir="auto">12.2. Upon termination, the Client shall pay all outstanding fees for services rendered up to the date of termination.</p>
<p dir="auto">12.3. Upon termination, the Service Provider shall provide the Client with all necessary data and assistance to transition to another service provider within 30 days of the termination date, with a 2-week leeway period for complex migrations.</p>
<p dir="auto">12.4. The Service Provider shall provide a transition plan within 10 business days of receiving a termination notice.</p>
<h2 data-heading="13. GOVERNING LAW" dir="auto">13. GOVERNING LAW</h2>
<p dir="auto">13.1. This SLA shall be governed by and construed in accordance with the laws of Queensland, Australia.</p>
<p dir="auto">13.2. Any dispute arising out of or in connection with this SLA shall be subject to the exclusive jurisdiction of the courts of Queensland, Australia.</p>
<h2 data-heading="14. ENTIRE AGREEMENT" dir="auto">14. ENTIRE AGREEMENT</h2>
<p dir="auto">14.1. This SLA constitutes the entire agreement between the parties with respect to the subject matter hereof and supersedes all prior and contemporaneous agreements or communications.</p>
<p dir="auto">14.2. Any modifications to this SLA must be made in writing and signed by authorized representatives of both parties.</p>
<h2 data-heading="15. PRIVACY AND DATA PROTECTION" dir="auto">15. PRIVACY AND DATA PROTECTION</h2>
<p dir="auto">15.1. The Service Provider shall comply with the Privacy Act 1988 (Cth) and the Australian Privacy Principles in relation to any personal information collected, used, or disclosed in connection with this SLA.</p>
<p dir="auto">15.2. The Service Provider shall implement appropriate technical and organizational measures to protect the Client's data from unauthorized access, use, disclosure, alteration, or destruction.</p>
<p dir="auto">15.3. The Service Provider shall not use the Client's data for any purpose other than providing the services under this SLA.</p>
<p dir="auto">15.4. Upon termination of this SLA, the Service Provider shall return or securely destroy all Client data as directed by the Client, except as required by law.</p>
<h2 data-heading="16. INTELLECTUAL PROPERTY" dir="auto">16. INTELLECTUAL PROPERTY</h2>
<p dir="auto">16.1. Each party retains all rights, title, and interest in and to its pre-existing intellectual property.</p>
<p dir="auto">16.2. The Client owns all rights, title, and interest in and to the content, data, and materials provided by the Client for use in connection with the services.</p>
<p dir="auto">16.3. The Service Provider grants the Client a non-exclusive, non-transferable license to use any Service Provider intellectual property necessary for the Client to receive the services during the term of this SLA.</p>
<h2 data-heading="17. INSURANCE" dir="auto">17. INSURANCE</h2>
<p dir="auto">17.1. The Service Provider shall maintain, at its own expense, professional indemnity insurance and public liability insurance with coverage sufficient to cover its obligations under this SLA.</p>
<p dir="auto">17.2. Upon request, the Service Provider shall provide the Client with certificates of insurance evidencing the required coverage.</p>
<h2 data-heading="18. FORCE MAJEURE" dir="auto">18. FORCE MAJEURE</h2>
<p dir="auto">18.1. Neither party shall be liable for any failure or delay in performance due to circumstances beyond their reasonable control, including but not limited to acts of God, natural disasters, terrorism, war, civil unrest, labor disputes, or government actions.</p>
<p dir="auto">18.2. The affected party shall notify the other party of the force majeure event as soon as reasonably possible and use commercially reasonable efforts to resume performance promptly.</p>
<p dir="auto">18.3. If a force majeure event continues for more than thirty (30) days, either party may terminate this SLA upon written notice to the other party.</p>
<h2 data-heading="19. DISPUTE RESOLUTION" dir="auto">19. DISPUTE RESOLUTION</h2>
<p dir="auto">19.1. The parties shall attempt to resolve any dispute arising out of or in connection with this SLA through good faith negotiations.</p>
<p dir="auto">19.2. If the parties are unable to resolve the dispute through negotiations within 30 days, either party may refer the dispute to mediation by a mediator appointed by the Australian Disputes Centre.</p>
<p dir="auto">19.3. If the dispute is not resolved through mediation within 60 days, either party may commence legal proceedings in the courts of Queensland, Australia.</p>
<p dir="auto">19.4. Nothing in this clause prevents either party from seeking urgent injunctive or equitable relief from a court of competent jurisdiction.</p>
<h2 data-heading="20. NOTICES" dir="auto">20. NOTICES</h2>
<p dir="auto">20.1. All notices required under this SLA shall be in writing and delivered by email with confirmation of receipt, or by registered mail to the addresses specified in this SLA.</p>
<p dir="auto">20.2. Notices shall be deemed received:<br>
a) If delivered by email, upon receipt of confirmation of delivery<br>
b) If delivered by registered mail, 3 business days after posting</p>
<p dir="auto">20.3. Each party shall notify the other party of any change in contact details within 5 business days.</p>
<h2 data-heading="EXECUTION" dir="auto">EXECUTION</h2>
<p dir="auto">This Service Level Agreement is executed as of the date first written above.</p>
<p dir="auto"><strong>SERVICE PROVIDER:</strong></p>
<p dir="auto">Name: <strong><strong><strong><strong><strong><strong><strong>__</strong></strong></strong></strong></strong></strong></strong></p>
<p dir="auto">Title: <strong><strong><strong><strong><strong><strong><strong>___</strong></strong></strong></strong></strong></strong></strong></p>
<p dir="auto">Signature: <strong><strong><strong><strong><strong><strong>___</strong></strong></strong></strong></strong></strong></p>
<p dir="auto">Date: <strong><strong><strong><strong><strong><strong><strong>___</strong></strong></strong></strong></strong></strong></strong></p>
<p dir="auto"><strong>CLIENT:</strong></p>
<p dir="auto">Name: <strong><strong><strong><strong><strong><strong><strong>__</strong></strong></strong></strong></strong></strong></strong></p>
<p dir="auto">Title: <strong><strong><strong><strong><strong><strong><strong>___</strong></strong></strong></strong></strong></strong></strong></p>
<p dir="auto">Signature: <strong><strong><strong><strong><strong><strong>___</strong></strong></strong></strong></strong></strong></p>
<p dir="auto">Date: <strong><strong><strong><strong><strong><strong><strong>___</strong></strong></strong></strong></strong></strong></strong></p>
    </body>
</html>
---
creation_date: 2025-05-14
modification_date: 2025-05-14
type: meeting
date: 2025-05-14
time: 11:52
location: Online
participants: [margaret sharpe, jordan pacey]
tags: [meeting]
project: 
area: Software-Development
status: scheduled
---
# 1- Correspondence 19/05/2025

## Details
- **Date**: 2025-05-14
- **Time**: 11:53
- **Location**: Online
- **Participants**: Margaret, Jordan

## Agenda
-Discuss Deployment plans and requirements
**-Payment**: presented high level pricing structure of paceyspace. Need to follow up with speicifc pricing with invoices. Aggregate as a paceyspace package to present a total pricing.
- mentoined of contract to sign and provided the basic outline and the main points of the contract. (mentioned is flexible, mentioned)
-**Security**: project and services requirements (image media heavy, watermarking, facebook integrations (for data replication), simple and free firewall to mitigate robots (which there have been impersonators, especially for maine coons/exotic cats), 

## Discussion Points
- <PERSON><PERSON><PERSON> mentioned she has been having a difficult time lately and is at a loss. May be suggesting payment extensions and prolonged overdue payments temporarily. Make sure to clearly label the overdue payment penalties in the contract for her to discuss
	- she did mention setting up a direct debit though, moving forward
	- 

## Decisions
-

## Action Items
- [ ] to send margaret email with instruction on how to update the facebook page details with contacts etc.
	- [ ] send instructions how to add admins
	- [ ] add phone number
	- [ ] add email
	- [ ] add website

## Related Projects
```dataview
LIST
FROM "1-Projects"
WHERE contains(file.content, "[[1-Correspondence]]") OR project = ""
```

## Related Areas
```dataview
LIST
FROM "2-Areas"
WHERE contains(file.content, "[[1-Correspondence]]") OR area = "Software-Development"
```

## Quick Links
- [[2025-05-14|Daily Note]]
- [[Tasks]]

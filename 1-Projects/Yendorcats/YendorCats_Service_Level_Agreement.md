# SERVICE LEVEL AGREEMENT (SLA)
## CI/CD PIPELINE MANAGEMENT AND WEBSITE HOSTING SERVICES

**BETWEEN:**

**PaceySpace Digital** ("Service Provider")
ABN: [Insert ABN]
Address: Caboolture, Queensland, Australia 4510
Email: <EMAIL>
Phone: 07 2111 0402
GST Registered: Yes

**AND**

**[Client Name]** ("Client")
ABN: [Insert Client ABN]
Address: [Insert Client Address]
Email: [Insert Client Email]
Phone: [Insert Client Phone]

## 1. SERVICE OVERVIEW

This Service Level Agreement (SLA) defines the terms and conditions under which PaceySpace Digital will provide CI/CD pipeline management and website hosting services for the YendorCats website using the Enhance Control Panel and associated infrastructure. This agreement is made in Queensland, Australia and is subject to Australian law, including the Australian Consumer Law.

## 2. TERM

2.1. This SLA shall commence on [Start Date] and continue for a period of twelve (12) months ("Initial Term").

2.2. This SLA shall automatically renew for successive twelve (12) month periods ("Renewal Term") unless either party provides written notice of non-renewal at least sixty (60) days prior to the end of the current Term.

## 3. SERVICES PROVIDED

### 3.1. CI/CD Pipeline Management

The Service Provider shall:

a) Implement and maintain a continuous integration and continuous deployment (CI/CD) pipeline for the YendorCats website using the Enhance Control Panel.

b) Configure automated testing, build, and deployment processes.

c) Monitor the CI/CD pipeline for failures and resolve issues promptly.

d) Maintain version control and code quality assurance.

e) Provide documentation of the CI/CD pipeline configuration.

f) Implement security best practices within the CI/CD pipeline.

g) Complete initial CI/CD pipeline setup within [X] days of the commencement date, with a 2-week leeway period for unforeseen circumstances.

### 3.2. Website Hosting Services

The Service Provider shall:

a) Provision and maintain appropriate infrastructure for hosting the YendorCats website, including:
   - Docker containers for all services (API, Database, File Uploader)
   - MariaDB database for data storage
   - Backblaze B2 for S3-compatible storage
   - Cloudflare integration for CDN, WAF, and DNS services

b) Configure and maintain HTTPS for secure communication.

c) Implement and maintain backup systems for website data.

d) Monitor website performance and availability.

e) Apply security patches and updates to all infrastructure components.

f) Provide DNS management services.

g) Complete initial hosting infrastructure setup within [X] days of the commencement date, with a 2-week leeway period for unforeseen circumstances.

### 3.3. Scalability Services

The Service Provider shall:

a) Implement infrastructure capable of handling traffic spikes during cat breeding times and major announcements.

b) Configure auto-scaling capabilities where appropriate.

c) Provision replicated instances as needed to maintain performance during high-traffic periods.

d) Monitor resource utilization and scale resources proactively.

e) Implement load balancing for distributed traffic handling.

f) Optimize database performance for high-traffic scenarios.

g) Implement scalability features within [X] days of the commencement date, with a 2-week leeway period for unforeseen circumstances.

h) Scale resources within 24 hours of receiving notice from the Client about anticipated traffic spikes, with a 2-week advance notice requirement from the Client for major events.

## 4. SERVICE LEVELS

### 4.1. Website Uptime

a) The Service Provider guarantees 99.9% uptime for the YendorCats website, measured monthly.

b) Uptime excludes scheduled maintenance windows, which shall be communicated to the Client at least 48 hours in advance.

c) Scheduled maintenance shall be performed during off-peak hours (typically between 11:00 PM and 5:00 AM AEST) unless otherwise agreed.

### 4.2. Performance Metrics

a) Page load time: The website shall load within 3 seconds for 90% of page requests under normal traffic conditions.

b) API response time: API endpoints shall respond within 500ms for 95% of requests under normal traffic conditions.

c) Database query performance: 95% of database queries shall complete within 200ms.

### 4.3. Scalability Metrics

a) The infrastructure shall be capable of handling a minimum of 500 concurrent users during normal operations.

b) During peak periods (cat breeding times and major announcements), the infrastructure shall scale to handle up to 2,000 concurrent users without significant performance degradation.

c) Auto-scaling shall initiate when resource utilization exceeds 70% for more than 5 minutes.

### 4.4. Backup and Recovery

a) Database backups shall be performed daily with a retention period of 30 days.

b) File storage backups shall be performed weekly with a retention period of 90 days.

c) Recovery Point Objective (RPO): Maximum data loss in the event of a disaster shall not exceed 24 hours.

d) Recovery Time Objective (RTO): In the event of a disaster, services shall be restored within 4 hours of the incident being reported.

## 5. SUPPORT AND INCIDENT RESPONSE

### 5.1. Support Hours

a) Standard support hours: Monday to Friday, 9:00 AM to 5:00 PM AEST, excluding public holidays in Queensland, Australia.

b) After-hours support: Available for critical incidents only.

### 5.2. Incident Classification and Response Times

| Severity | Description | Response Time | Resolution Time |
|----------|-------------|---------------|-----------------|
| Critical | Complete service outage or security breach | 30 minutes | 4 hours |
| High | Partial service outage or severe performance degradation | 2 hours | 8 hours |
| Medium | Non-critical feature unavailable or minor performance issues | 4 hours | 24 hours |
| Low | Cosmetic issues or feature requests | 8 hours | 72 hours |

### 5.3. Incident Reporting

a) The Client shall report incidents via:
   - Email: <EMAIL>
   - Phone: 07 2111 0402
   - Support portal: [Insert Support Portal URL]

b) Each incident report shall include:
   - Description of the issue
   - Time the issue was first observed
   - Steps to reproduce (if applicable)
   - Impact on business operations

### 5.4. Incident Management Process

a) Acknowledgment: The Service Provider shall acknowledge receipt of all incident reports within the specified response time.

b) Investigation: The Service Provider shall investigate the root cause of the incident.

c) Resolution: The Service Provider shall implement a solution to resolve the incident within the resolution times specified in Section 5.2, with a 2-week leeway period for complex issues that require third-party intervention or extensive development work.

d) Communication: The Service Provider shall provide regular updates on the status of critical and high-severity incidents at intervals not exceeding 4 hours for critical incidents and 8 hours for high-severity incidents.

e) Post-incident review: For critical incidents, the Service Provider shall provide a post-incident report within 48 hours of resolution.

## 6. MONITORING AND REPORTING

### 6.1. Monitoring

a) The Service Provider shall implement comprehensive monitoring of all infrastructure components, including:
   - Server health and resource utilization
   - Database performance
   - Website availability and performance
   - CI/CD pipeline status
   - Security events

b) Automated alerts shall be configured for potential issues.

### 6.2. Regular Reporting

a) The Service Provider shall provide monthly performance reports including:
   - Website uptime statistics
   - Performance metrics
   - Incident summary
   - Backup status
   - Security status
   - Resource utilization trends

b) Monthly reports shall be delivered within 10 business days after the end of each month, with a 2-week leeway period for complex reporting requirements.

c) Quarterly service review meetings shall be scheduled to discuss performance, issues, and improvement opportunities. These meetings shall be scheduled at least 2 weeks in advance.

## 7. FEES AND PAYMENT

### 7.1. Service Fees

a) Base monthly fee: $[Amount] per month for the services described in Section 3.

b) All fees are in Australian Dollars (AUD) and include Goods and Services Tax (GST) unless otherwise specified.

c) Additional fees for resource scaling during high-traffic periods shall be calculated based on actual usage according to the following rates:
   - Additional container instances: $[Amount] per instance per day
   - Additional database resources: $[Amount] per GB of storage per month
   - Additional bandwidth: $[Amount] per GB beyond the included allocation
   - Additional S3 storage: $[Amount] per GB per month

d) The Service Provider will provide a tax invoice compliant with Australian GST requirements for all charges.

### 7.2. Payment Terms

a) The Client shall be invoiced monthly in advance for the base fee.

b) Additional usage fees shall be invoiced monthly in arrears.

c) All invoices are due within fourteen (14) days of the invoice date.

d) Late payments are subject to a grace period of one (1) month from the due date.

e) For annual contract terms, a grace period of three (3) months is provided, in accordance with Australian Consumer Law.

f) If payment is not received within the applicable grace period, the Service Provider reserves the right to suspend services with 48 hours written notice.

g) The Service Provider will provide a 2-week leeway period before applying late payment fees, which will be charged at [X]% per month on the outstanding amount.

## 8. SERVICE CREDITS

### 8.1. Service Credit Calculation

a) If the Service Provider fails to meet the uptime guarantee specified in Section 4.1, the Client shall be entitled to service credits according to the following schedule:

| Monthly Uptime | Service Credit (% of monthly fee) |
|----------------|-----------------------------------|
| 99.0% - 99.9% | 10% |
| 98.0% - 98.9% | 25% |
| 97.0% - 97.9% | 50% |
| Below 97.0% | 100% |

b) Service credits shall be applied to the next monthly invoice.

### 8.2. Service Credit Request Process

a) The Client must request service credits in writing within 30 days of the end of the month in which the service level failure occurred.

b) Service credit requests shall include:
   - Date and time of the service disruption
   - Description of the service disruption
   - Impact on the Client's operations

c) The Service Provider shall review all service credit requests and respond within 14 days.

## 9. CLIENT RESPONSIBILITIES

The Client shall:

a) Provide timely access to necessary information, systems, and personnel.

b) Designate a primary point of contact for communication with the Service Provider.

c) Promptly report any issues or incidents.

d) Pay all invoices in accordance with the payment terms.

e) Provide reasonable notice of anticipated traffic spikes or special events, with at least 2 weeks advance notice for major events.

f) Comply with all applicable laws and regulations regarding website content and data.

g) Maintain control over website content and ensure it does not violate any laws or third-party rights.

h) Respond to Service Provider requests for information or approvals within 5 business days, with a 2-week leeway period for complex requests.

i) Provide feedback on deliverables within 10 business days, with a 2-week leeway period for complex deliverables.

## 10. CONFIDENTIALITY

10.1. Each party shall maintain the confidentiality of all confidential information disclosed by the other party.

10.2. Confidential information includes, but is not limited to, business plans, customer data, financial information, and technical specifications.

10.3. This obligation of confidentiality shall survive the termination of this SLA.

## 11. LIMITATION OF LIABILITY

11.1. The Service Provider's total liability under this SLA shall not exceed the total fees paid by the Client in the twelve (12) months preceding the event giving rise to the liability.

11.2. Neither party shall be liable for any indirect, special, incidental, or consequential damages.

## 12. TERMINATION

12.1. Either party may terminate this SLA for cause if the other party materially breaches this SLA and fails to cure such breach within thirty (30) days of receiving written notice.

12.2. Upon termination, the Client shall pay all outstanding fees for services rendered up to the date of termination.

12.3. Upon termination, the Service Provider shall provide the Client with all necessary data and assistance to transition to another service provider within 30 days of the termination date, with a 2-week leeway period for complex migrations.

12.4. The Service Provider shall provide a transition plan within 10 business days of receiving a termination notice.

## 13. GOVERNING LAW

13.1. This SLA shall be governed by and construed in accordance with the laws of Queensland, Australia.

13.2. Any dispute arising out of or in connection with this SLA shall be subject to the exclusive jurisdiction of the courts of Queensland, Australia.

## 14. ENTIRE AGREEMENT

14.1. This SLA constitutes the entire agreement between the parties with respect to the subject matter hereof and supersedes all prior and contemporaneous agreements or communications.

14.2. Any modifications to this SLA must be made in writing and signed by authorized representatives of both parties.

## 15. PRIVACY AND DATA PROTECTION

15.1. The Service Provider shall comply with the Privacy Act 1988 (Cth) and the Australian Privacy Principles in relation to any personal information collected, used, or disclosed in connection with this SLA.

15.2. The Service Provider shall implement appropriate technical and organizational measures to protect the Client's data from unauthorized access, use, disclosure, alteration, or destruction.

15.3. The Service Provider shall not use the Client's data for any purpose other than providing the services under this SLA.

15.4. Upon termination of this SLA, the Service Provider shall return or securely destroy all Client data as directed by the Client, except as required by law.

## 16. INTELLECTUAL PROPERTY

16.1. Each party retains all rights, title, and interest in and to its pre-existing intellectual property.

16.2. The Client owns all rights, title, and interest in and to the content, data, and materials provided by the Client for use in connection with the services.

16.3. The Service Provider grants the Client a non-exclusive, non-transferable license to use any Service Provider intellectual property necessary for the Client to receive the services during the term of this SLA.

## 17. INSURANCE

17.1. The Service Provider shall maintain, at its own expense, professional indemnity insurance and public liability insurance with coverage sufficient to cover its obligations under this SLA.

17.2. Upon request, the Service Provider shall provide the Client with certificates of insurance evidencing the required coverage.

## 18. FORCE MAJEURE

18.1. Neither party shall be liable for any failure or delay in performance due to circumstances beyond their reasonable control, including but not limited to acts of God, natural disasters, terrorism, war, civil unrest, labor disputes, or government actions.

18.2. The affected party shall notify the other party of the force majeure event as soon as reasonably possible and use commercially reasonable efforts to resume performance promptly.

18.3. If a force majeure event continues for more than thirty (30) days, either party may terminate this SLA upon written notice to the other party.

## 19. DISPUTE RESOLUTION

19.1. The parties shall attempt to resolve any dispute arising out of or in connection with this SLA through good faith negotiations.

19.2. If the parties are unable to resolve the dispute through negotiations within 30 days, either party may refer the dispute to mediation by a mediator appointed by the Australian Disputes Centre.

19.3. If the dispute is not resolved through mediation within 60 days, either party may commence legal proceedings in the courts of Queensland, Australia.

19.4. Nothing in this clause prevents either party from seeking urgent injunctive or equitable relief from a court of competent jurisdiction.

## 20. NOTICES

20.1. All notices required under this SLA shall be in writing and delivered by email with confirmation of receipt, or by registered mail to the addresses specified in this SLA.

20.2. Notices shall be deemed received:
   a) If delivered by email, upon receipt of confirmation of delivery
   b) If delivered by registered mail, 3 business days after posting

20.3. Each party shall notify the other party of any change in contact details within 5 business days.

## EXECUTION

This Service Level Agreement is executed as of the date first written above.

**SERVICE PROVIDER:**

Name: ______________________________

Title: _______________________________

Signature: ___________________________

Date: _______________________________

**CLIENT:**

Name: ______________________________

Title: _______________________________

Signature: ___________________________

Date: _______________________________

Great question! There are several efficient ways to set S3 metadata for bulk operations. Let me show you the most practical approaches for tagging lots of images:

## **S3 Metadata Management Options**

### **1. AWS CLI (Most Efficient for Bulk Operations)**

#### **Install AWS CLI**
```bash
# macOS
brew install awscli

# Configure with your Backblaze B2 credentials
aws configure --profile backblaze
# AWS Access Key ID: your_b2_key_id
# AWS Secret Access Key: your_b2_application_key
# Default region name: us-west-004
# Default output format: json
```

#### **Bulk Metadata Update Script**
```bash
./scripts/bulk-metadata-update.sh

# Choose option 1 for category-wide updates
# Choose option 2 for specific cats
```


### **2. CSV-Based Bulk Update Script**
```bash
# Generate sample CSV
python3 scripts/metadata-from-csv.py sample

# Edit the CSV with your data
# Then update S3
python3 scripts/metadata-from-csv.py update your_metadata.csv
```


### **3. Sample CSV Template**
```csv
s3_key,cat_name,age,date_taken,description,breed,gender,color,personality,bloodline
studs/louie/512587981_24370061079264957_2212939644238241976_n.jpg,Louie,3.5,2024-01-15T10:30:00Z,Magnificent Maine Coon stud with excellent bloodlines,Maine Coon,M,Brown Tabby,Gentle and playful,Champion bloodline
studs/louie/511983065_24370060875931644_7622850250066422508_n.jpg,Louie,3.5,2024-01-16T11:45:00Z,Beautiful portrait showing his majestic features,Maine Coon,M,Brown Tabby,Gentle and playful,Champion bloodline
studs/soren/512647539_24370542745883457_5378466661915711333_n.jpg,Soren,4.2,2024-02-10T09:15:00Z,Stunning Maine Coon with impressive size,Maine Coon,M,Silver Tabby,Calm and dignified,European bloodline
studs/soren/511991955_24370542529216812_1432263306594410478_n.jpg,Soren,4.2,2024-02-11T14:20:00Z,Profile shot showcasing his noble bearing,Maine Coon,M,Silver Tabby,Calm and dignified,European bloodline
studs/dennis/511273385_24370176772586721_2403458704145963937_n.jpg,Dennis,2.8,2024-03-05T16:30:00Z,Young stud with promising features,Maine Coon,M,Red Tabby,Energetic and friendly,American bloodline
studs/dennis/511009104_24370176572586741_2917073592025095125_n.jpg,Dennis,2.8,2024-03-06T12:10:00Z,Action shot showing his playful nature,Maine Coon,M,Red Tabby,Energetic and friendly,American bloodline

```

### **4. Web-Based Metadata Editor (Optional)**
```bash
# Open the web editor
open scripts/metadata-editor.html
```
### **Step 1: Configure AWS CLI**

```bash
# Install AWS CLI

brew install awscli

# Configure for Backblaze B2

aws configure --profile backblaze

# Enter your B2 credentials:

# - Key ID

# - Application Key  

# - Region: us-west-004

# - Format: json
```

### **Step 2: Update Script Configuration**

Edit the scripts with your actual credentials:

*metadata-from-csv.py*
```bash
# Update these with your actual B2 credentials

AWS_ACCESS_KEY_ID = "your_actual_b2_key_id"
AWS_SECRET_ACCESS_KEY = 
"your_actual_b2_application_key"
```
### **Step 3: Test with Single File**
```bash
# Check current metadata

aws s3api head-object \

    --bucket yendor \

    --key "studs/louie/image.jpg" \

    --endpoint-url="https://s3.us-west-004.

    backblazeb2.com" \

    --profile backblaze \

    --query 'Metadata'
```
## **Metadata Fields Supported**

|Field|Purpose|Example|
|---|---|---|
|`cat-name`|Cat's name|"Louie"|
|`age`|Age in years|"3.5"|
|`date-taken`|Photo date|"2024-01-15T10:30:00Z"|
|`description`|Photo description|"Beautiful Maine Coon stud"|
|`breed`|Cat breed|"Maine Coon"|
|`gender`|M/F|"M"|
|`color`|Color/pattern|"Brown Tabby"|
|`personality`|Traits|"Gentle and playful"|
|`bloodline`|Lineage|"Champion bloodline"|

## **Workflow Recommendations**

### **For Large Batches (100+ images)**

1. **Use AWS CLI script** for speed
2. **Category-wide updates** first
3. **Individual refinements** later

### **For Precise Control**

1. **Create CSV** with exact metadata
2. **Use Python script** for updates
3. **Verify results** with API calls

### **For Ongoing Management**

1. **Web interface** for new uploads
2. **CSV exports** for backup
3. **Bulk scripts** for corrections

## **Performance Tips**

### **Rate Limiting**

- Add `sleep 0.5` between requests
- Use smaller batches (50-100 files)
- Monitor for 429 errors
---
creation_date: 2025-07-05
modification_date: 2025-07-05
type: project
status: active
priority: medium
deadline: 2025-08-04
project_owner: Jordan
project_client: Personal
completion_percentage: 0
estimated_hours: 10
tags: [para/projects, software-dev]
area: Software-Development
start_date: 2025-07-05
---

# Project Development Journal and Notes

## Overview
<!-- Brief description of the project -->

## Objectives
<!-- What are you trying to achieve? -->
-

## Success Criteria
<!-- How will you know when the project is successful? -->
-
---
## Augment  #25-07-25 #bugfix
### 📋 **Current Status**

|Component|Status|Details|
|---|---|---|
|**API Server**|✅ Running|HTTP on port 5003|
|**Database**|✅ Working|SQLite with persistence|
|**Admin User**|✅ Created|Username: `admin`, Password: `EoGTa)F=c6ew22X*`|
|**Swagger UI**|✅ Available|`http://localhost:5003/swagger`|
|**Frontend**|✅ Serving|Static files being served|
|**API Endpoints**|✅ Functional|`/api/cats`, `/api/auth/login`, etc.|

### 🔄 **Temporary Modifications Made**

- **Secrets Manager Service**: Temporarily disabled
- **S3 Storage Service**: Temporarily disabled
- **File Watcher**: Temporarily disabled
- **HTTPS**: Disabled in favor of HTTP-only
- **Service Dependencies**: Modified to work without external services

The API is now **fully operational** for development and testing! 🚀

### 🔧 **Root Causes Identified & Fixed**

1. **Dependency Injection Issues**:
    - `AuthService` dependency on disabled `ISecretsManagerService` ✅ Fixed
    - `ImageService` dependency on disabled `IS3StorageService` ✅ Fixed
2. **HTTPS Configuration Issue**:
    - Docker container trying to bind to HTTPS without SSL certificate ✅ Fixed
    - Changed to HTTP-only: `ASPNETCORE_URLS=http://+:80`
3. **File Watcher Hanging**:
    - `UseFileWatcher` middleware causing hang when frontend directory missing ✅ Fixed
    - Temporarily disabled for debugging
- - **✅ Starting successfully** without hanging
- **✅ Serving HTTP requests** on `http://localhost:5003`
- **✅ Database working** (SQLite with persistence)
- **✅ API endpoints functional** (`/api/cats`, `/swagger`, etc.)
- **✅ Frontend being served** (HTML content)
- **✅ Authentication endpoints responding** (even if rejecting invalid credentials)
---

## Tasks
<!-- List of tasks to complete -->
- [ ] **Yendor new photos Tasks july**:
	- [ ] **Stud**s:
	      - louie baby 12 months
	      - soren baby pic - 5 later this year
	      - dennis is 11 august
- 🚨 **CRITICAL: IMMEDIATE ACTION REQUIRED**
	-  **EXPOSED CREDENTIALS THAT NEED ROTATION:**
		1. **Backblaze B2 Access Key:** `xxx360000000001`
		2. **Backblaze B2 Secret Key:** `xxx7g5A5I`
			1. **Log into your Backblaze B2 account**
			2. **Delete/Disable the exposed API key**
			3. **Generate new API credentials**
			4. **Update your local `.env` file** with new credentials

### **Setup Your Local Environment:**

Copy

# 1. Copy the template file

cp .env.template .env

# 2. Edit .env with your actual values

# Replace all placeholder values with real credentials

# 3. Copy configuration templates

cp backend/YendorCats.API/appsettings.Development.json.

template backend/YendorCats.API/appsettings.Development.

json

# 4. Edit the development config with your values

## 📋 **SECURITY CHECKLIST**

- ✅ **Secrets removed from repository**
- ✅ **Enhanced .gitignore rules**
- ✅ **Template files created**
- ✅ **Documentation provided**
- ⚠️ **PENDING: Rotate exposed Backblaze B2 credentials**
- ⚠️ **PENDING: Setup local .env file**
- ⚠️ **PENDING: Generate strong JWT secret**

## 🔐 **NEXT STEPS**

1. **Immediately rotate Backblaze B2 credentials**
2. **Setup your local `.env` file** using the template
3. **Generate a strong JWT secret** (minimum 32 characters)
4. **Test your application** with the new configuration
5. **Consider implementing HashiCorp Vault** for production

## 📚 **Security Resources**

- **SECURITY_SETUP.md** - Complete setup guide
- **.env.template** - Environment variables template
- **Configuration templates** - Safe configuration examples

### **Tags**

#security #credentials #apikeys #backblaze #jwt #configuration #gitignore #templates #rotation

**grabboing all photos and put on photo gallery**

- ✅ **SECURITY_SETUP.md** - Complete setup guide

### **Setup Your Local Environment:**

Copy

# 1. Copy the template file

cp .env.template .env

# 2. Edit .env with your actual values

# Replace all placeholder values with real credentials

# 3. Copy configuration templates

cp backend/YendorCats.API/appsettings.Development.json.

template backend/YendorCats.API/appsettings.Development.

json

# 4. Edit the development config with your values

## 📋 **SECURITY CHECKLIST**

- ✅ **Secrets removed from repository**
- ✅ **Enhanced .gitignore rules**
- ✅ **Template files created**
- ✅ **Documentation provided**
- ⚠️ **PENDING: Rotate exposed Backblaze B2 credentials**
- ⚠️ **PENDING: Setup local .env file**
- ⚠️ **PENDING: Generate strong JWT secret**

## 🔐 **NEXT STEPS**

1. **Immediately rotate Backblaze B2 credentials**
2. **Setup your local `.env` file** using the template
3. **Generate a strong JWT secret** (minimum 32 characters)
4. **Test your application** with the new configuration
5. **Consider implementing HashiCorp Vault** for production

## 📚 **Security Resources**

- **SECURITY_SETUP.md** - Complete setup guide
- **.env.template** - Environment variables template
- **Configuration templates** - Safe configuration examples

### **Tags**

#security #credentials #apikeys #backblaze #jwt #configuration #gitignore #templates #rotation

## Timeline
- **Start Date**: 2025-07-05
- **Deadline**: 2025-08-04
- **Milestones**:
  - [ ] Initial Planning - 2025-07-12
  - [ ] Development - 2025-07-19
  - [ ] Testing - 2025-07-26
  - [ ] Completion - 2025-08-04

## Resources
<!-- Links to relevant resources -->

  

  

STRAY FILE notes (README?) in the wrong file name:

from AdminController.cs (not the write file content:

```markdown
# YendorCats - Exotic Cat Breeder Website

A modern website for an exotic cat breeder, featuring a visually appealing gallery with a .NET 8 backend API and rich metadata for cat images.

## Project Overview

This project consists of three main components:

1. **Frontend**: A responsive, visually appealing website built with vanilla JavaScript, HTML5, and CSS3.

2. **Backend API**: A .NET 8 API using Kestrel server to manage cats, images, and metadata.

3. **File Uploader**: A Node.js microservice for uploading images with rich metadata to S3-compatible storage.

## Tech Stack

### Frontend

- HTML5
- CSS3 (mobile-first approach)
- Vanilla JavaScript (no frameworks)
- Responsive design for all device sizes
- Performance optimized image loading
- Accessibility compliant

### Backend

- .NET 8 with C#
- Kestrel server
- Entity Framework Core for data access
- JWT authentication
- MariaDB for database (hosted on Enhance Control Panel)
- Repository pattern
- Dependency injection
- Backblaze B2 S3-compatible storage for images

### File Uploader

- Node.js
- Express
- AWS SDK for S3
- Multer for file handling
- Bootstrap for responsive design

### Deployment

- Enhance Control Panel for hosting
- Docker containers for all services
- Cloudflare for CDN, WAF, and DNS
- Doppler for secrets management (optional)
- MariaDB for database

## Project Structure

YendorCats/
├── backend/                    # .NET 8 backend API
│   ├── YendorCats.API/         # API project
│   │   ├── Controllers/        # API endpoints
│   │   ├── Data/               # Database context and configurations
│   │   ├── Models/             # Data models
│   │   ├── Services/           # Business logic and services
│   │   ├── Configuration/      # App configuration
│   │   ├── Middleware/         # Custom middleware
│   │   └── Program.cs          # App startup and configuration
│   └── YendorCats.Tests/       # Unit tests
├── frontend/                   # Frontend website
│   ├── css/                    # Stylesheets
│   ├── js/                     # JavaScript files
│   ├── images/                 # Static images
│   │   ├── gallery/            # Cat gallery images
│   │   └── breeds/             # Breed information images
│   └── index.html              # Main HTML file
├── tools/                      # Tools and utilities
│   ├── file-uploader/          # File uploader microservice
│   │   ├── public/             # Static files
│   │   ├── server.js           # Main server file
│   │   └── Dockerfile          # Docker configuration
│   └── deployment/             # Deployment scripts
└── docs/                       # Documentation
    ├── API.md                  # API documentation
    ├── Deployment.md           # Deployment guide
    └── Testing.md              # Testing guide
```

  

## Getting Started

  

### Development Environment Setup

  

1. **Prerequisites**:

   - .NET 8 SDK

   - Node.js (v18+)

   - Docker and Docker Compose

   - MariaDB (or Docker container)

   - Backblaze B2 account (or MinIO for local development)

  

### Quick Start for Photo Upload System (New Feature)

  

The new photo upload system allows easy uploading of cat photos with automatic profile linking and timeline creation. Follow these steps to test it:

  

1. **Configure Backblaze B2 Credentials**:

   ```bash

# Edit the development configuration file

   nano backend/YendorCats.API/appsettings.Development.json

  

   # Replace the placeholder values with your actual Backblaze B2 credentials:

   # "AccessKey": "YOUR_BACKBLAZE_B2_ACCESS_KEY_HERE"

   # "SecretKey": "YOUR_BACKBLAZE_B2_SECRET_KEY_HERE"

```

  

2. **Start the Backend API**:

   ```bash

cd backend/YendorCats.API

   dotnet restore

   dotnet run

```

   The API will be available at `https://localhost:5001`

  

3. **Test the Photo Upload System**:

   - Open your browser and navigate to `https://localhost:5001/upload.html`

   - Try uploading a cat photo with metadata

   - View cat profiles at `https://localhost:5001/profiles.html`

   - Check individual cat timelines at `https://localhost:5001/cat-profile.html?cat=CatName`

  

4. **Verify Upload Functionality**:

   - Upload photos with different cat names to test profile linking

   - Check that photos appear in the correct categories

   - Verify timeline ordering by date taken

   - Test the search and filtering features

  

2. **Backend Setup**:

   ```bash

# Clone the repository

   git clone https://github.com/yourusername/yendorcats.git

   cd yendorcats

  

   # Navigate to the backend directory

   cd backend/YendorCats.API

  

   # Install required packages

   dotnet restore

  

   # Update the connection string in `appsettings.Development.json`

   # For example:

   # "ConnectionStrings": {

   #   "DefaultConnection": "Server=localhost;Database=YendorCats;User=root;Password=yourpassword;"

   # }

  

   # Run database migrations

   dotnet ef database update

  

   # Start the API in development mode

   dotnet run

```

  

   The API will be available at `https://localhost:5001` and `http://localhost:5000`

  

3. **File Uploader Setup**:

   ```bash

# Navigate to the file uploader directory

   cd tools/file-uploader

  

   # Install dependencies

   npm install

  

   # Create a .env file with your configuration

   # Example:

   # AWS_S3_BUCKET_NAME=your-bucket-name

   # AWS_S3_REGION=us-west-004

   # AWS_S3_ENDPOINT=https://s3.us-west-004.backblazeb2.com

   # AWS_S3_ACCESS_KEY=your-access-key

   # AWS_S3_SECRET_KEY=your-secret-key

   # API_BASE_URL=http://localhost:5000

   # PORT=5002

  

   # Start the file uploader service

   npm start

```

  

   The file uploader will be available at `http://localhost:5002`

  

4. **Frontend Setup**:

   ```bash

# The frontend is served by the backend API

   # No additional setup is required

   # Access the frontend at http://localhost:5000

```

  

### Docker Setup

  

1. **Build and run with Docker Compose**:

   ```bash

# Create a .env file with your configuration

   # Example:

   # MYSQL_ROOT_PASSWORD=your-root-password

   # MYSQL_DATABASE=yendorcats

   # MYSQL_USER=yendorcats

   # MYSQL_PASSWORD=your-password

   # AWS_S3_BUCKET_NAME=your-bucket-name

   # AWS_S3_REGION=us-west-004

   # AWS_S3_ENDPOINT=https://s3.us-west-004.backblazeb2.com

   # AWS_S3_ACCESS_KEY=your-access-key

   # AWS_S3_SECRET_KEY=your-secret-key

   # API_BASE_URL=http://localhost:5000

  

   # Build and start the containers

   docker-compose up -d

```

  

2. **Access the services**:

   - Frontend: http://localhost:80

   - Backend API: http://localhost:5000

   - File Uploader: http://localhost:5002

  

### Enhance Control Panel Deployment

  

For detailed deployment instructions, see the [Deployment Guide](1-Projects/Yendorcats/YendorCats%20Deployment%20Guide.md).

  

1. **Prerequisites**:

   - Enhance Control Panel account with a VPS

   - Backblaze B2 account for S3-compatible storage

   - Domain name with DNS access

   - Cloudflare account (optional but recommended)

   - Doppler account for secrets management (optional)

  

2. **Server Setup**:

   - Set up a VPS with Enhance Control Panel

   - Configure DNS for your domain

   - Set up SSL certificates with Let's Encrypt

  

3. **Database Setup**:

   - Deploy MariaDB container

   - Initialize database schema

   - Configure connection settings

  

4. **Secrets Management**:

   - Set up Doppler (recommended) or use environment files

   - Configure secrets for all services

  

5. **Application Deployment**:

   - Deploy API service container

   - Deploy File Uploader service container

   - Deploy Frontend with Nginx

   - Configure routing and SSL

  

6. **Backup Configuration**:

   - Set up automated backups in Enhance Control Panel

   - Configure backup retention policies

   - Set up email notifications for backup status

  

## S3 Metadata Implementation

  

The project uses Backblaze B2 S3-compatible storage with rich metadata for cat gallery images. The metadata includes:

  

### Required Fields

- Name of the cat

- Gender (M/F)

- Date uploaded (set automatically)

- File format

- Content type

  

### Optional User-Inputted Fields

- Description

- Age

- Bloodline

- Breed

- Hair color

- Date taken (photo)

- Personality

- Category

- Tags (for filtering and search)

- Mother/Father information

  

### System-Generated Fields

- File size

- Image dimensions

- Upload IP and user agent

- Upload session ID

  

This rich metadata enables advanced filtering and search capabilities, allowing users to find cats based on various criteria.

  

For detailed information about the metadata implementation, see the [S3 Metadata Implementation Guide](1-Projects/Yendorcats/YendorCats%20S3%20Metadata%20Implementation.md).

  

## Application Configuration

  

### Development Configuration

  

In development mode, the application uses settings from `appsettings.Development.json`:

  

```json

{

  "ConnectionStrings": {

    "DefaultConnection": "Server=localhost;Database=YendorCats;User=root;Password=yourpassword;"

  },

  "S3Settings": {

    "BucketName": "yendorcats",

    "Region": "us-west-004",

    "Endpoint": "https://s3.us-west-004.backblazeb2.com",

    "AccessKey": "your-access-key",

    "SecretKey": "your-secret-key"

  },

  "JwtSettings": {

    "Secret": "your-development-secret-key-at-least-32-characters",

    "Issuer": "YendorCatsApi",

    "Audience": "YendorCatsClients",

    "ExpiryMinutes": 60,

    "RefreshExpiryDays": 7

  }

}

```

  

### Production Configuration

  

In production, the application uses environment variables or Doppler for secrets management:

  

1. **Database Connection**: Retrieved from environment variables or Doppler

2. **S3 Configuration**: Retrieved from environment variables or Doppler

3. **JWT Configuration**: Retrieved from environment variables or Doppler

  

## API Endpoints

  

The backend provides the following API endpoints:

  

### Photo Upload (New)

- `POST /api/PhotoUpload/upload` - Upload a cat photo with metadata

- `GET /api/PhotoUpload/cat/{catName}` - Get photos for a specific cat (cat profile)

- `GET /api/PhotoUpload/profiles` - Get all cat profiles with photo counts

  

### Gallery

- `GET /api/gallery` - Get all gallery images

- `GET /api/gallery/category/{category}` - Get images for a specific category

- `GET /api/gallery/metadata/{category}/{fileName}` - Get detailed metadata for a specific image

- `GET /api/gallery/metadata/fields` - Get all available metadata fields for filtering

- `GET /api/gallery/search` - Search images based on metadata criteria

  

### Authentication

- `POST /api/auth/register` - Register a new user

- `POST /api/auth/login` - Login and receive JWT token

- `POST /api/auth/refresh-token` - Refresh an expired JWT token

- `POST /api/auth/logout` - Logout and invalidate refresh token

  

### Cats

- `GET /api/cats` - Get all cats (with optional filtering)

- `GET /api/cats/{id}` - Get a specific cat by ID

- `POST /api/cats` - Add a new cat (admin only)

- `PUT /api/cats/{id}` - Update a cat (admin only)

- `DELETE /api/cats/{id}` - Delete a cat (admin only)

  

### Users

- `GET /api/users` - Get all users (admin only)

- `GET /api/users/{id}` - Get user by ID (admin only)

- `GET /api/users/me` - Get current user profile

- `PUT /api/users/{id}` - Update user (admin or self)

  

### File Uploader

- `POST /upload` - Upload a file with metadata

  

## Frontend Features

  

### Core Features

- Image-heavy responsive gallery for showcasing cats

- Filtering and searching available cats

- Contact form for inquiries

- Admin login for content management

- Testimonials section

- Breed information pages

- Modern, visually appealing design

- Performance optimized for fast loading

  

### New Photo Upload System Features

- **Drag & Drop Upload Interface** (`/upload.html`)

  - Modern file upload with drag-and-drop support

  - Real-time file validation and preview

  - Comprehensive metadata form for cat information

  - Upload progress tracking and status feedback

  - Recent uploads display with local storage

  

- **Cat Profile Pages** (`/cat-profile.html?cat=CatName`)

  - Individual cat profile pages with photo timelines

  - Grid and timeline view modes for photo browsing

  - Photo modal with keyboard navigation

  - Profile statistics and metadata display

  - Share functionality with native Web Share API

  

- **Profiles Overview** (`/profiles.html`)

  - Complete listing of all cat profiles

  - Search and filtering by breed, gender, photo count

  - Statistics dashboard showing total cats and photos

  - Quick actions for uploading and browsing

  

## Image Management

  

### File Uploader Service

  

The project includes a dedicated Node.js microservice for uploading cat images with rich metadata. This service provides:

  

- A user-friendly web interface for uploading images

- Form fields for all metadata properties

- Validation for required fields

- Preview of selected images before upload

- Automatic extraction of file metadata

- Upload to Backblaze B2 with S3-compatible API

  

For detailed information about the file uploader service, see the [File Uploader Service Guide](1-Projects/Yendorcats/YendorCats%20File%20Uploader%20Service.md).

  

### Metadata Management

  

The system supports two methods for managing cat image metadata:

  

1. **S3 Object Metadata (Primary Method)**:

   - All metadata is stored as S3 object metadata

   - Metadata is retrieved directly from S3 when displaying images

   - Supports rich metadata with many fields

   - Enables advanced filtering and search capabilities

  

2. **Filename Parsing (Fallback Method)**:

   - For backward compatibility with existing images

   - Extracts basic metadata from filenames

   - Filename format: `[cat's name]-[age]-[date(DDMMYY)]-[order].jpg`

   - Example: `Georgia-2.6-230325-1.jpg` (Name: Georgia, Age: 2.6 years, Date: March 23, 2025, Order: 1)

  

### Adding New Images

  

To add new cat images to the website:

  

1. **Using the File Uploader (Recommended)**:

   - Navigate to the file uploader interface (typically at `/upload`)

   - Select an image file

   - Fill in the metadata fields

   - Click "Upload"

  

2. **Manual Upload (Alternative)**:

   - Prepare your image files with the correct naming format

   - Place them in the appropriate category folder under `resources/`

   - The system will extract basic metadata from the filename

  

### Image Categories

  

Images are organized into the following categories:

  

- `studs/` - Male cats

- `queens/` - Female cats

- `kittens/` - Kittens

- `gallery/` - General gallery images

  

### Search and Filtering

  

The system provides advanced search and filtering capabilities based on metadata:

  

- Filter by category (studs, queens, kittens, gallery)

- Filter by gender (M/F)

- Filter by breed

- Filter by bloodline

- Filter by hair color

- Filter by age range

- Filter by tags

- Sort by date, name, or age

- Sort in ascending or descending order

  

## Security Features

  

- JWT authentication with refresh tokens

- Password hashing with salt

- HTTPS support

- CORS configuration

- Input validation

- Authorization based on user roles

- Error handling middleware

- SQL injection prevention through EF Core

- XSS protection

- Doppler for secure secrets management

- Docker containers for consistent deployment

  

## Testing

  

For detailed testing instructions, see the [Testing Guide](1-Projects/Yendorcats/YendorCats%20Testing%20Guide.md).

  

### Testing the New Photo Upload System

  

#### Prerequisites for Testing

1. **Backblaze B2 Setup**:

   ```bash

# Get your Backblaze B2 credentials from your account

   # You'll need: Application Key ID and Application Key

   # Create a bucket named "yendorcats-images-dev"

```

  

2. **Configure Development Environment**:

   ```bash

# Edit the configuration file

   nano backend/YendorCats.API/appsettings.Development.json

  

   # Update these values with your actual credentials:

   "AWS": {

     "UseCredentialsFromSecrets": false,

     "S3": {

       "AccessKey": "YOUR_ACTUAL_BACKBLAZE_B2_KEY_ID",

       "SecretKey": "YOUR_ACTUAL_BACKBLAZE_B2_APPLICATION_KEY",

       "BucketName": "yendorcats-images-dev"

     }

   }

```

  

#### Step-by-Step Testing Process

  

1. **Start the Application**:

   ```bash

cd backend/YendorCats.API

   dotnet restore

   dotnet run

```

   Wait for "Now listening on: https://localhost:5001"

  

2. **Test Photo Upload Interface**:

   - Open browser to `https://localhost:5001/upload.html`

   - Try uploading a cat photo (JPG, PNG, GIF, or WebP under 10MB)

   - Fill in cat metadata (name is required)

   - Verify upload success message

  

3. **Test Cat Profile Creation**:

   - Navigate to `https://localhost:5001/profiles.html`

   - Verify your uploaded cat appears in the profiles list

   - Click on the cat profile to view timeline

  

4. **Test Profile Timeline**:

   - Go to `https://localhost:5001/cat-profile.html?cat=YourCatName`

   - Verify photos appear in timeline

   - Test grid vs timeline view toggle

   - Test photo modal by clicking on images

  

5. **Test Multiple Photos for Same Cat**:

   - Upload 2-3 more photos with the same cat name

   - Verify they all appear in the same profile

   - Check timeline ordering by date

  

#### Verification Checklist

  

- [ ] Photos upload successfully to Backblaze B2

- [ ] Cat profiles are created automatically

- [ ] Photos are linked to correct cat profiles

- [ ] Timeline displays photos in date order

- [ ] Search and filtering work correctly

- [ ] Photo modal displays metadata properly

- [ ] Mobile responsive design works

- [ ] Error handling works for invalid files

  

### Quick Testing Steps (Original)

  

1. Run unit tests for the backend:

   ```bash

cd backend

   dotnet test

```

  

2. Run unit tests for the file uploader:

   ```bash

cd tools/file-uploader

   npm test

```

  

3. Run integration tests:

   ```bash

cd backend/YendorCats.Tests

   dotnet test --filter Category=Integration

```

  

## Documentation

  

For comprehensive documentation, see the [Table of Contents - YendorCats](Table%20of%20Contents%20-%20YendorCats.md).

  

### Key Documentation

  

- [S3 Metadata Implementation](1-Projects/Yendorcats/YendorCats%20S3%20Metadata%20Implementation.md)

- [File Uploader Service](1-Projects/Yendorcats/YendorCats%20File%20Uploader%20Service.md)

- [Deployment Guide](1-Projects/Yendorcats/YendorCats%20Deployment%20Guide.md)

- [Testing Guide](1-Projects/Yendorcats/YendorCats%20Testing%20Guide.md)

  

## Troubleshooting

  

### Photo Upload System Issues

  

1. **Upload Fails with "Network Error"**:

   - Check that the backend API is running on `https://localhost:5001`

   - Verify CORS settings allow frontend domain

   - Check browser console for detailed error messages

  

2. **"Invalid Credentials" or S3 Upload Errors**:

   - Verify Backblaze B2 credentials in `appsettings.Development.json`

   - Ensure bucket name exists and is accessible

   - Check that bucket has proper permissions for uploads

   - Verify the S3 endpoint URL is correct for your region

  

3. **Photos Upload but Don't Appear in Profiles**:

   - Check browser console for JavaScript errors

   - Verify the API endpoints are responding correctly

   - Check that cat names match exactly (case-sensitive)

   - Ensure database connection is working

  

4. **File Upload Validation Errors**:

   - Supported formats: JPG, JPEG, PNG, GIF, WebP

   - Maximum file size: 10MB

   - Ensure file is not corrupted

  

5. **Cat Profile Pages Show "Cat Not Found"**:

   - Check URL parameter format: `?cat=ExactCatName`

   - Verify cat name spelling and case

   - Ensure photos were uploaded successfully

  

### Common Issues (General)

  

1. **Database Connection Issues**:

   - In development: Check your connection string in `appsettings.Development.json`

   - In production: Ensure your environment variables are correctly set

  

2. **S3 Storage Issues**:

   - Verify your Backblaze B2 credentials

   - Check bucket permissions

   - Ensure the S3 endpoint URL is correct

  

3. **Docker Deployment**:

   - If the container fails to start, check Docker logs: `docker logs yendorcats-api`

   - Ensure the environment variables are correctly set

  

4. **Frontend-Backend Communication**:

   - Check CORS settings in `Program.cs` to ensure your frontend domain is allowed

  

### Logs

  

- In development: Logs are output to the console

- In production: Logs are stored in the Docker container and can be viewed with `docker logs yendorcats-api`

  

## Contributing

  

1. Fork the repository

2. Create a feature branch: `git checkout -b feature/my-feature`

3. Commit your changes: `git commit -am 'Add my feature'`

4. Push to the branch: `git push origin feature/my-feature`

5. Submit a pull request

  

## License

  

This project is licensed under the MIT License - see the LICENSE file for details.

  

## Acknowledgments

  

- Photos used in the gallery are placeholders and should be replaced with actual cat photos

- Font Awesome for icons

- Google Fonts for typography - see the LICENSE file for details.

  

## Acknowledgments

  

- Photos used in the gallery are placeholders and should be replaced with actual cat photos

- Font Awesome for icons

- Google Fonts for typography

- Photos used in the gallery are placeholders and should be replaced with actual cat photos

- Font Awesome for icons

- Google Fonts for typography

## Acknowledgments

  

- Photos used in the gallery are placeholders and should be replaced with actual cat photos

- Font Awesome for icons

- Google Fonts for typography

- Photos used in the gallery are placeholders and should be replaced with actual cat photos

- Font Awesome for icons

- Google Fonts for typography

  

```

```

  

PROMPTS

  

i am not seeing any images load from my b1 bucketa. They identification details and its related information are:

yendorCreated:May 3, 2025

Bucket ID:ad20ecddf688956e9b650316

Type:Public

File Lifecycle:Keep prior versions for days

Snapshots:0

Current Files:10

Current Size:35.4 MB

Endpoint:s3.us-west-004.backblazeb2.com

Encryption:Disabled

  

The application key details for the bucket are:

keyID:

004d0cd685eb5360000000001

keyName:

yendor-s3-access

bucketName:

yendor

expiration:

-

namePrefix:

(none)

  

I will input the application key (bucket secret) myself if you can show me where to do that.  Please make sure that the images are being fetched successfully, and the right images are being requested from the right galley locations (queens section displays the photos from the queens folder, studs displays images in studs folder etc)

## Related Areas
```dataview
LIST
FROM "2-Areas"
WHERE contains(file.name, "Project Development Journal and Notes") OR area = "Software-Development"
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(file.content, "[[Project Development Journal and Notes]]") OR contains(tags, "software-dev")
LIMIT 5
```

## Meeting Notes
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  date as "Date",
  participants as "Participants"
FROM #meeting
WHERE contains(related, "Project Development Journal and Notes") OR contains(file.name, "Project Development Journal and Notes")
SORT date DESC
```

## Progress Updates
<!-- Regular updates on project progress -->
### 2025-07-05 - Initial Setup
- Project created
- Initial planning started

## Quick Links
- [[Project Development Journal and Notes Meeting|New Meeting]]
- [[Project Development Journal and Notes Resource|New Resource]]
- [[1-Projects|All Projects]]

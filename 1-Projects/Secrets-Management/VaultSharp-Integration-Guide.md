---

# VaultSharp Integration Guide for .NET Applications

## Overview

VaultSharp is the official .NET client library for HashiCorp Vault. This guide covers implementing VaultSharp in your YendorCats API project for secure secrets management with practical examples and best practices.

## Tags
#vaultsharp #dotnet #csharp #vault #integration #secrets #api #yendorcats #nuget

---

## VaultSharp Package Installation

### 1. Install via Package Manager

```bash
# Install VaultSharp NuGet package
dotnet add package VaultSharp

# Or via Package Manager Console in Visual Studio
Install-Package VaultSharp
```

### 2. Current Implementation Review

Your project already includes VaultSharp v********:

<augment_code_snippet path="backend/YendorCats.API/YendorCats.API.csproj" mode="EXCERPT">
```xml
<PackageReference Include="VaultSharp" Version="********" />
```
</augment_code_snippet>

---

## VaultSharp Client Configuration

### 1. Basic Token Authentication

<augment_code_snippet path="backend/YendorCats.API/Services/ISecretsManagerService.cs" mode="EXCERPT">
```csharp
using VaultSharp;
using VaultSharp.V1.AuthMethods.Token;
using VaultSharp.V1.Commons;

public class SecretsManagerService : ISecretsManagerService
{
    private readonly IVaultClient _vaultClient;
    
    public SecretsManagerService(IConfiguration configuration)
    {
        // Get Vault configuration
        string vaultAddress = configuration["Vault:Address"] ?? "http://localhost:8200";
        string vaultToken = configuration["Vault:Token"] ?? 
                           Environment.GetEnvironmentVariable("VAULT_TOKEN") ?? 
                           string.Empty;
        
        // Create token authentication method
        var authMethod = new TokenAuthMethodInfo(vaultToken);
        
        // Configure Vault client settings
        var vaultClientSettings = new VaultClientSettings(vaultAddress, authMethod);
        
        // Initialize Vault client
        _vaultClient = new VaultClient(vaultClientSettings);
    }
}
```
</augment_code_snippet>

### 2. AppRole Authentication (Recommended for Production)

```csharp
using VaultSharp.V1.AuthMethods.AppRole;

public class VaultClientFactory
{
    public static IVaultClient CreateAppRoleClient(IConfiguration configuration)
    {
        string vaultAddress = configuration["Vault:Address"];
        string roleId = configuration["Vault:RoleId"] ?? 
                       Environment.GetEnvironmentVariable("VAULT_ROLE_ID");
        string secretId = configuration["Vault:SecretId"] ?? 
                         Environment.GetEnvironmentVariable("VAULT_SECRET_ID");
        
        // Create AppRole authentication method
        var authMethod = new AppRoleAuthMethodInfo(roleId, secretId);
        
        // Configure client settings
        var vaultClientSettings = new VaultClientSettings(vaultAddress, authMethod);
        
        return new VaultClient(vaultClientSettings);
    }
}
```

### 3. Advanced Configuration Options

```csharp
public class AdvancedVaultClientFactory
{
    public static IVaultClient CreateClient(VaultConfiguration config)
    {
        var authMethod = config.AuthMethod switch
        {
            "token" => new TokenAuthMethodInfo(config.Token),
            "approle" => new AppRoleAuthMethodInfo(config.RoleId, config.SecretId),
            _ => throw new ArgumentException($"Unsupported auth method: {config.AuthMethod}")
        };
        
        var settings = new VaultClientSettings(config.Address, authMethod)
        {
            // Configure HTTP client settings
            VaultServiceTimeout = TimeSpan.FromSeconds(30),
            UseVaultTokenHeaderInsteadOfAuthorizationHeader = true,
            
            // TLS configuration
            MyHttpClientProviderFunc = () =>
            {
                var handler = new HttpClientHandler();
                
                if (config.SkipTlsVerify)
                {
                    handler.ServerCertificateCustomValidationCallback = 
                        (message, cert, chain, errors) => true;
                }
                
                return new HttpClient(handler)
                {
                    Timeout = TimeSpan.FromSeconds(30)
                };
            }
        };
        
        return new VaultClient(settings);
    }
}

public class VaultConfiguration
{
    public string Address { get; set; } = "http://localhost:8200";
    public string AuthMethod { get; set; } = "token";
    public string Token { get; set; } = string.Empty;
    public string RoleId { get; set; } = string.Empty;
    public string SecretId { get; set; } = string.Empty;
    public bool SkipTlsVerify { get; set; } = false;
}
```

---

## Secret Operations with VaultSharp

### 1. Reading Secrets (KV v2)

```csharp
public async Task<AppSecrets> GetAppSecretsAsync()
{
    try
    {
        string secretPath = _configuration["Vault:SecretPath"] ?? "secret/yendorcats/app-secrets";
        
        // Read secret from KV v2 engine
        var secret = await _vaultClient.V1.Secrets.KeyValue.V2.ReadSecretAsync(secretPath);
        
        // Extract data from response
        var secretData = secret.Data.Data;
        
        // Map to strongly-typed object
        var appSecrets = new AppSecrets
        {
            DbConnectionString = secretData.GetValueOrDefault("DbConnectionString")?.ToString() ?? string.Empty,
            JwtSecret = secretData.GetValueOrDefault("JwtSecret")?.ToString() ?? string.Empty,
            JwtIssuer = secretData.GetValueOrDefault("JwtIssuer")?.ToString() ?? string.Empty,
            JwtAudience = secretData.GetValueOrDefault("JwtAudience")?.ToString() ?? string.Empty,
            JwtExpiryMinutes = int.Parse(secretData.GetValueOrDefault("JwtExpiryMinutes")?.ToString() ?? "60"),
            RefreshExpiryDays = int.Parse(secretData.GetValueOrDefault("RefreshExpiryDays")?.ToString() ?? "7"),
            S3AccessKey = secretData.GetValueOrDefault("S3AccessKey")?.ToString() ?? string.Empty,
            S3SecretKey = secretData.GetValueOrDefault("S3SecretKey")?.ToString() ?? string.Empty,
            S3SessionToken = secretData.GetValueOrDefault("S3SessionToken")?.ToString() ?? string.Empty,
            ApiKey = secretData.GetValueOrDefault("ApiKey")?.ToString() ?? string.Empty
        };
        
        return appSecrets;
    }
    catch (VaultApiException ex)
    {
        _logger.LogError(ex, "Vault API error: {StatusCode} - {Message}", ex.HttpStatusCode, ex.Message);
        throw;
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Failed to retrieve secrets from Vault");
        throw;
    }
}
```

### 2. Writing Secrets

```csharp
public async Task<bool> UpdateSecretAsync(string path, Dictionary<string, object> secrets)
{
    try
    {
        await _vaultClient.V1.Secrets.KeyValue.V2.WriteSecretAsync(path, secrets);
        _logger.LogInformation("Successfully updated secret at path: {Path}", path);
        return true;
    }
    catch (VaultApiException ex)
    {
        _logger.LogError(ex, "Failed to update secret at path: {Path}", path);
        return false;
    }
}

// Usage example
var newSecrets = new Dictionary<string, object>
{
    ["JwtSecret"] = "new-jwt-secret-key",
    ["ApiKey"] = "new-api-key-value"
};

await UpdateSecretAsync("secret/yendorcats/app-secrets", newSecrets);
```

### 3. Partial Secret Updates (Patch)

```csharp
public async Task<bool> PatchSecretAsync(string path, Dictionary<string, object> updates)
{
    try
    {
        // First, read existing secret
        var existingSecret = await _vaultClient.V1.Secrets.KeyValue.V2.ReadSecretAsync(path);
        var existingData = existingSecret.Data.Data;
        
        // Merge with updates
        foreach (var update in updates)
        {
            existingData[update.Key] = update.Value;
        }
        
        // Write back the merged data
        await _vaultClient.V1.Secrets.KeyValue.V2.WriteSecretAsync(path, existingData);
        
        _logger.LogInformation("Successfully patched secret at path: {Path}", path);
        return true;
    }
    catch (VaultApiException ex)
    {
        _logger.LogError(ex, "Failed to patch secret at path: {Path}", path);
        return false;
    }
}
```

### 4. Secret Metadata Operations

```csharp
public async Task<SecretMetadata> GetSecretMetadataAsync(string path)
{
    try
    {
        var metadata = await _vaultClient.V1.Secrets.KeyValue.V2.ReadSecretMetadataAsync(path);
        
        return new SecretMetadata
        {
            CreatedTime = metadata.Data.CreatedTime,
            UpdatedTime = metadata.Data.UpdatedTime,
            CurrentVersion = metadata.Data.CurrentVersion,
            MaxVersions = metadata.Data.MaxVersions,
            Versions = metadata.Data.Versions?.ToDictionary(
                kvp => kvp.Key,
                kvp => new VersionInfo
                {
                    CreatedTime = kvp.Value.CreatedTime,
                    DeletionTime = kvp.Value.DeletionTime,
                    Destroyed = kvp.Value.Destroyed
                })
        };
    }
    catch (VaultApiException ex)
    {
        _logger.LogError(ex, "Failed to get metadata for path: {Path}", path);
        throw;
    }
}

public class SecretMetadata
{
    public DateTimeOffset CreatedTime { get; set; }
    public DateTimeOffset UpdatedTime { get; set; }
    public int CurrentVersion { get; set; }
    public int MaxVersions { get; set; }
    public Dictionary<string, VersionInfo>? Versions { get; set; }
}

public class VersionInfo
{
    public DateTimeOffset CreatedTime { get; set; }
    public DateTimeOffset? DeletionTime { get; set; }
    public bool Destroyed { get; set; }
}
```

---

## Error Handling and Resilience

### 1. Comprehensive Error Handling

```csharp
public async Task<T> ExecuteVaultOperationAsync<T>(Func<Task<T>> operation, string operationName)
{
    const int maxRetries = 3;
    const int baseDelayMs = 1000;
    
    for (int attempt = 1; attempt <= maxRetries; attempt++)
    {
        try
        {
            return await operation();
        }
        catch (VaultApiException ex) when (ex.HttpStatusCode == HttpStatusCode.Forbidden)
        {
            _logger.LogError(ex, "Access denied for Vault operation: {Operation}", operationName);
            throw; // Don't retry on auth failures
        }
        catch (VaultApiException ex) when (ex.HttpStatusCode == HttpStatusCode.NotFound)
        {
            _logger.LogError(ex, "Secret not found for operation: {Operation}", operationName);
            throw; // Don't retry on not found
        }
        catch (VaultApiException ex) when (IsRetryableError(ex))
        {
            if (attempt == maxRetries)
            {
                _logger.LogError(ex, "Vault operation failed after {Attempts} attempts: {Operation}", 
                    maxRetries, operationName);
                throw;
            }
            
            var delay = TimeSpan.FromMilliseconds(baseDelayMs * Math.Pow(2, attempt - 1));
            _logger.LogWarning(ex, "Vault operation failed, retrying in {Delay}ms (attempt {Attempt}/{MaxRetries}): {Operation}", 
                delay.TotalMilliseconds, attempt, maxRetries, operationName);
            
            await Task.Delay(delay);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error in Vault operation: {Operation}", operationName);
            throw;
        }
    }
    
    throw new InvalidOperationException("This should never be reached");
}

private static bool IsRetryableError(VaultApiException ex)
{
    return ex.HttpStatusCode == HttpStatusCode.InternalServerError ||
           ex.HttpStatusCode == HttpStatusCode.BadGateway ||
           ex.HttpStatusCode == HttpStatusCode.ServiceUnavailable ||
           ex.HttpStatusCode == HttpStatusCode.GatewayTimeout ||
           ex.HttpStatusCode == HttpStatusCode.TooManyRequests;
}
```

### 2. Circuit Breaker Pattern

```csharp
public class VaultCircuitBreaker
{
    private readonly object _lock = new object();
    private int _failureCount = 0;
    private DateTime _lastFailureTime = DateTime.MinValue;
    private readonly int _failureThreshold;
    private readonly TimeSpan _timeout;
    private CircuitState _state = CircuitState.Closed;
    
    public VaultCircuitBreaker(int failureThreshold = 5, TimeSpan? timeout = null)
    {
        _failureThreshold = failureThreshold;
        _timeout = timeout ?? TimeSpan.FromMinutes(1);
    }
    
    public async Task<T> ExecuteAsync<T>(Func<Task<T>> operation)
    {
        if (_state == CircuitState.Open)
        {
            if (DateTime.UtcNow - _lastFailureTime < _timeout)
            {
                throw new CircuitBreakerOpenException("Circuit breaker is open");
            }
            
            _state = CircuitState.HalfOpen;
        }
        
        try
        {
            var result = await operation();
            OnSuccess();
            return result;
        }
        catch (Exception)
        {
            OnFailure();
            throw;
        }
    }
    
    private void OnSuccess()
    {
        lock (_lock)
        {
            _failureCount = 0;
            _state = CircuitState.Closed;
        }
    }
    
    private void OnFailure()
    {
        lock (_lock)
        {
            _failureCount++;
            _lastFailureTime = DateTime.UtcNow;
            
            if (_failureCount >= _failureThreshold)
            {
                _state = CircuitState.Open;
            }
        }
    }
}

public enum CircuitState
{
    Closed,
    Open,
    HalfOpen
}

public class CircuitBreakerOpenException : Exception
{
    public CircuitBreakerOpenException(string message) : base(message) { }
}
```

---

## Caching and Performance

### 1. In-Memory Caching with Expiration

```csharp
public class CachedSecretsManagerService : ISecretsManagerService
{
    private readonly ISecretsManagerService _innerService;
    private readonly IMemoryCache _cache;
    private readonly ILogger<CachedSecretsManagerService> _logger;
    private readonly TimeSpan _cacheExpiry;
    
    public CachedSecretsManagerService(
        ISecretsManagerService innerService,
        IMemoryCache cache,
        ILogger<CachedSecretsManagerService> logger,
        IConfiguration configuration)
    {
        _innerService = innerService;
        _cache = cache;
        _logger = logger;
        _cacheExpiry = TimeSpan.FromMinutes(
            configuration.GetValue<int>("Vault:CacheExpiryMinutes", 15));
    }
    
    public async Task<AppSecrets> GetAppSecretsAsync()
    {
        const string cacheKey = "app-secrets";
        
        if (_cache.TryGetValue(cacheKey, out AppSecrets? cachedSecrets) && cachedSecrets != null)
        {
            _logger.LogDebug("Retrieved secrets from cache");
            return cachedSecrets;
        }
        
        _logger.LogDebug("Cache miss, retrieving secrets from Vault");
        var secrets = await _innerService.GetAppSecretsAsync();
        
        _cache.Set(cacheKey, secrets, _cacheExpiry);
        _logger.LogDebug("Cached secrets for {ExpiryMinutes} minutes", _cacheExpiry.TotalMinutes);
        
        return secrets;
    }
    
    public async Task ClearCacheAsync()
    {
        _cache.Remove("app-secrets");
        _logger.LogInformation("Cleared secrets cache");
    }
}
```

### 2. Background Secret Refresh

```csharp
public class BackgroundSecretRefreshService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<BackgroundSecretRefreshService> _logger;
    private readonly TimeSpan _refreshInterval;
    
    public BackgroundSecretRefreshService(
        IServiceProvider serviceProvider,
        ILogger<BackgroundSecretRefreshService> logger,
        IConfiguration configuration)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _refreshInterval = TimeSpan.FromMinutes(
            configuration.GetValue<int>("Vault:RefreshIntervalMinutes", 30));
    }
    
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var secretsService = scope.ServiceProvider.GetRequiredService<CachedSecretsManagerService>();
                
                // Pre-warm cache
                await secretsService.GetAppSecretsAsync();
                _logger.LogDebug("Refreshed secrets cache");
                
                await Task.Delay(_refreshInterval, stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing secrets cache");
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
            }
        }
    }
}
```

---

## Dependency Injection Setup

### 1. Service Registration

```csharp
// In Program.cs
public static void ConfigureVaultServices(this IServiceCollection services, IConfiguration configuration)
{
    // Register Vault configuration
    services.Configure<VaultConfiguration>(configuration.GetSection("Vault"));
    
    // Register Vault client factory
    services.AddSingleton<IVaultClient>(provider =>
    {
        var config = provider.GetRequiredService<IOptions<VaultConfiguration>>().Value;
        return AdvancedVaultClientFactory.CreateClient(config);
    });
    
    // Register secrets manager service
    services.AddSingleton<SecretsManagerService>();
    
    // Decorate with caching
    services.AddSingleton<ISecretsManagerService>(provider =>
    {
        var innerService = provider.GetRequiredService<SecretsManagerService>();
        var cache = provider.GetRequiredService<IMemoryCache>();
        var logger = provider.GetRequiredService<ILogger<CachedSecretsManagerService>>();
        var configuration = provider.GetRequiredService<IConfiguration>();
        
        return new CachedSecretsManagerService(innerService, cache, logger, configuration);
    });
    
    // Register background refresh service
    services.AddHostedService<BackgroundSecretRefreshService>();
    
    // Register circuit breaker
    services.AddSingleton<VaultCircuitBreaker>();
}

// Usage in Program.cs
builder.Services.ConfigureVaultServices(builder.Configuration);
```

### 2. Health Checks Integration

```csharp
public class VaultHealthCheck : IHealthCheck
{
    private readonly IVaultClient _vaultClient;
    private readonly ILogger<VaultHealthCheck> _logger;
    
    public VaultHealthCheck(IVaultClient vaultClient, ILogger<VaultHealthCheck> logger)
    {
        _vaultClient = vaultClient;
        _logger = logger;
    }
    
    public async Task<HealthCheckResult> CheckHealthAsync(
        HealthCheckContext context, 
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Check Vault server health
            var healthStatus = await _vaultClient.V1.System.GetHealthStatusAsync();
            
            if (healthStatus.Initialized && !healthStatus.Sealed)
            {
                return HealthCheckResult.Healthy("Vault is healthy and unsealed");
            }
            
            return HealthCheckResult.Unhealthy("Vault is sealed or not initialized");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Vault health check failed");
            return HealthCheckResult.Unhealthy("Cannot connect to Vault", ex);
        }
    }
}

// Register health check
builder.Services.AddHealthChecks()
    .AddCheck<VaultHealthCheck>("vault");
```

---

## Best Practices Summary

### Security
- ✅ Use AppRole authentication in production
- ✅ Implement proper error handling
- ✅ Use circuit breaker for resilience
- ✅ Cache secrets with appropriate expiry
- ✅ Log operations for audit trails

### Performance
- ✅ Cache frequently accessed secrets
- ✅ Use background refresh to pre-warm cache
- ✅ Implement retry logic with exponential backoff
- ✅ Monitor Vault client performance

### Maintenance
- ✅ Regular secret rotation
- ✅ Health checks for monitoring
- ✅ Proper logging and alerting
- ✅ Version control for secret schemas

---

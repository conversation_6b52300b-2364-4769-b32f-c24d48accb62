---

# <PERSON><PERSON><PERSON><PERSON><PERSON> Vault Setup Guide for YendorCats Project

## Overview

This guide covers setting up HashiCorp Vault for secure secrets management in your YendorCats cat breeder website project. Vault provides a free, self-hosted solution for managing API keys, database credentials, and other sensitive data across multiple client deployments.

## Tags
#hashicorp #vault #secrets-management #security #yendorcats #enhance-control-panel #vaultsharp #authentication

---

## What is HashiCorp Vault?

HashiCorp Vault is an open-source tool for securely storing and accessing secrets. A secret is anything that you want to tightly control access to, such as:

- **API keys** (AWS, Backblaze B2, payment processors)
- **Database credentials** (MySQL, PostgreSQL)
- **JWT signing keys** and certificates
- **Third-party service tokens**

### Key Benefits for Your Budget-Conscious Setup

- ✅ **Free Community Edition** - No licensing costs
- ✅ **Self-hosted** - Works with Enhance Control Panel
- ✅ **Secret Rotation** - Automated credential updates
- ✅ **Multi-client Support** - Separate secrets per client
- ✅ **Audit Logging** - Track secret access
- ✅ **High Security** - Encryption at rest and in transit

---

## Installation Options

### Option 1: Local Development Setup

```bash
# Download Vault binary
wget https://releases.hashicorp.com/vault/1.15.2/vault_1.15.2_linux_amd64.zip
unzip vault_1.15.2_linux_amd64.zip
sudo mv vault /usr/local/bin/

# Verify installation
vault version
```

### Option 2: Docker Setup (Recommended for Production)

```yaml
# docker-compose.vault.yml
version: '3.8'
services:
  vault:
    image: vault:1.15.2
    container_name: vault
    ports:
      - "8200:8200"
    environment:
      VAULT_DEV_ROOT_TOKEN_ID: myroot
      VAULT_DEV_LISTEN_ADDRESS: 0.0.0.0:8200
    cap_add:
      - IPC_LOCK
    volumes:
      - vault-data:/vault/data
      - vault-config:/vault/config
    command: vault server -dev

volumes:
  vault-data:
  vault-config:
```

### Option 3: Enhance Control Panel Deployment

```bash
# Create vault directory in your project
mkdir vault-config

# Create vault.hcl configuration
cat > vault-config/vault.hcl << EOF
ui = true
storage "file" {
  path = "/vault/data"
}
listener "tcp" {
  address = "0.0.0.0:8200"
  tls_disable = true
}
EOF
```

---

## Development Mode Quick Start

For immediate testing and development:

```bash
# Start Vault in development mode
vault server -dev

# In another terminal, set environment variables
export VAULT_ADDR='http://127.0.0.1:8200'
export VAULT_TOKEN='hvs.your-dev-token-here'

# Test connection
vault status
```

**⚠️ Warning**: Development mode stores data in memory and is NOT suitable for production!

---

## Production Setup Steps

### 1. Initialize Vault

```bash
# Start Vault server
vault server -config=vault-config/vault.hcl

# In another terminal, initialize
export VAULT_ADDR='http://127.0.0.1:8200'
vault operator init

# Save the unseal keys and root token securely!
```

### 2. Unseal Vault

```bash
# Use 3 of the 5 unseal keys
vault operator unseal <key1>
vault operator unseal <key2>
vault operator unseal <key3>
```

### 3. Enable KV Secrets Engine

```bash
# Login with root token
vault auth <root-token>

# Enable KV v2 secrets engine
vault secrets enable -path=secret kv-v2
```

---

## Storing Secrets for YendorCats

### Secret Structure

Your application expects secrets in this format:

```json
{
  "DbConnectionString": "Server=db;Database=YendorCats;User=user;Password=****;",
  "JwtSecret": "your-jwt-secret-key-here",
  "JwtIssuer": "YendorCatsApi",
  "JwtAudience": "YendorCatsClients",
  "JwtExpiryMinutes": 60,
  "RefreshExpiryDays": 7,
  "S3AccessKey": "your-backblaze-key-id",
  "S3SecretKey": "your-backblaze-secret-key",
  "S3SessionToken": "",
  "ApiKey": "additional-api-keys"
}
```

### Adding Secrets via CLI

```bash
# Store main application secrets
vault kv put secret/yendorcats/app-secrets \
  DbConnectionString="Server=db;Database=YendorCats;User=catuser;Password=secure****;" \
  JwtSecret="your-super-secure-jwt-key-256-bits-long" \
  JwtIssuer="YendorCatsApi" \
  JwtAudience="YendorCatsClients" \
  JwtExpiryMinutes=60 \
  RefreshExpiryDays=7 \
  S3AccessKey="your-backblaze-key-id" \
  S3SecretKey="your-backblaze-secret-key" \
  ApiKey="additional-service-keys"
```

### Multi-Client Setup

For multiple client deployments:

```bash
# Client 1 secrets
vault kv put secret/client1/yendorcats/app-secrets \
  DbConnectionString="Server=client1-db;Database=YendorCats;..." \
  JwtSecret="client1-specific-jwt-key" \
  S3AccessKey="client1-backblaze-key"

# Client 2 secrets  
vault kv put secret/client2/yendorcats/app-secrets \
  DbConnectionString="Server=client2-db;Database=YendorCats;..." \
  JwtSecret="client2-specific-jwt-key" \
  S3AccessKey="client2-backblaze-key"
```

---

## Configuration in YendorCats API

Your `appsettings.json` is already configured:

```json
{
  "Vault": {
    "Address": "http://localhost:8200",
    "Token": "your-vault-token-here",
    "SecretPath": "secret/yendorcats/app-secrets"
  }
}
```

### Environment Variables (Recommended)

Instead of hardcoding the token in config:

```bash
# Set environment variable
export VAULT_TOKEN="hvs.your-production-token"

# Or in your deployment script
echo "VAULT_TOKEN=hvs.your-production-token" >> .env
```

---

## Next Steps

1. **[Vault Security Configuration](./Vault-Security-Configuration.md)** - Secure your Vault setup
2. **[Secret Rotation Setup](./Vault-Secret-Rotation.md)** - Automate credential updates  
3. **[Multi-Client Deployment](./Vault-Multi-Client-Setup.md)** - Manage multiple client secrets
4. **[Backup and Recovery](./Vault-Backup-Recovery.md)** - Protect your secrets

---

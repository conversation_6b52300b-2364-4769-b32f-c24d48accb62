---
creation_date: 2025-06-24
modification_date: 2025-06-24
type: area
status: active
area: records
area_owner: Jordan
responsibility_level: high
review_frequency: weekly
tags: [para/areas, notes, documentation, records, history]
last_review_date: 2025-06-24
next_review_date: 2025-07-24
---
# Overview
<!-- Brief description of this area of responsibility -->
### Obsidian.nvim pilugin installation:
ERROR MESSAGE
```
Failed to run config for obsidian.nvim

...ocal/share/nvim/lazy/obsidian.nvim/lua/obsidian/path.lua:402: FileNotFoundError: /Users/<USER>/vaults/personal
* stoksracan. nvim/lua/obsidian/path. Lua: 402 in

resolve
- /obsidian.nvim/lua/obsidian/workspace.lua:79 in new_from_spec
- /obsidian.nvim/lua/obsidian/workspace.lua: 169 in get workspace for cwd ods1olan.nvincuaoosiolanworksoace. lua. 200

in cer tron oots
- /obs1d1an.nvim/ lua/obsidian/1n1t.lua:95 in setup

1:1

Top
```
## Current Focus
<!-- What's the current focus in this area? -->
-

## Key Responsibilities
<!-- List the key responsibilities in this area -->
-

## Regular Tasks
<!-- Recurring tasks in this area -->
- [ ] Daily:
- [ ] Weekly:
- [ ] Monthly:

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE area = "Personal" AND (status = "active" OR !contains(status, "completed"))
SORT priority ASC, deadline ASC
```

## Related Resources
```dataview
TABLE WITHOUT ID
  file.link as "Resource",
  source as "Source",
  file.mtime as "Last Modified"
FROM "3-Resources"
WHERE contains(file.content, "[[Untitled]]") OR area = "Administration"
SORT file.mtime DESC
LIMIT 10
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  file.mtime as "Last Modified"
FROM -"Templates"
WHERE contains(file.content, "[[Untitled]]")
SORT file.mtime DESC
LIMIT 5
```

## Key Metrics
<!-- Metrics to track in this area -->
-

## Quick Links
- [[Untitled Project|New Project]]
- [[Untitled Resource|New Resource]]
- [[Untitled Meeting|New Meeting]]
- [[2-Areas|All Areas]]

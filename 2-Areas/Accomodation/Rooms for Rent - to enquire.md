---
creation_date: 2025-06-29
modification_date: 2025-06-29
type: area
status: active
area: Personal
area_owner: Jordan
responsibility_level: high
review_frequency: monthly
tags: [para/areas, personal]
last_review_date: 2025-06-29
next_review_date: 2025-07-29
---
## Overview
<!-- Brief description of this area of responsibility -->

## Current Focus
<!-- What's the current focus in this area? -->
#Urgent
Look for a room to rent #ASAP when money for 2 weeks rent is organiseds
- https://www.facebook.com/marketplace/item/1956362281454188/?⁩
- https://www.facebook.com/marketplace/item/1164368471874901/?⁩
	- Ignore per month on the add, it’s usually per week ⁦
- https://www.facebook.com/marketplace/item/629898279389770/?
	- It would be $230/ week I think ⁦
- https://www.facebook.com/marketplace/item/1903048207176155/?⁩
	- These peopl are Indian but you’d have like your own little flat 
- ⁦https://www.facebook.com/marketplace/item/1040626651539892/?
	- 2 girls though 😩 
- https://www.facebook.com/marketplace/item/769283292439766/?⁩
- https://www.facebook.com/marketplace/item/3255317894606698/?⁩
	- This one sounds good ⁦
- https://www.facebook.com/marketplace/item/1077458491093267/?⁩
	- This is very expen $285 / week but would be close to train station 
- https://www.facebook.com/marketplace/item/675851215302895/?⁩
- This one might be ok. In Strathpine ⁦[https://www.facebook.com/marketplace/item/709815078678280/?⁩](https://www.facebook.com/marketplace/item/709815078678280/?%E2%81%A9)
- This sounds good too ⁦[https://www.facebook.com/marketplace/item/743199228129696/?⁩](https://www.facebook.com/marketplace/item/743199228129696/?%E2%81%A9)
- This also sounds good, at still mere ⁦[https://www.facebook.com/marketplace/item/6866172316817785/?⁩](https://www.facebook.com/marketplace/item/6866172316817785/?%E2%81%A9)
- Strathpine I think ⁦[https://www.facebook.com/marketplace/item/387354137598041/?⁩](https://www.facebook.com/marketplace/item/387354137598041/?%E2%81%A9)
- [https://www.facebook.com/marketplace/item/1763442114211329/?⁩](https://www.facebook.com/marketplace/item/1763442114211329/?%E2%81%A9)
- [https://www.facebook.com/marketplace/item/554720817692660/?⁩](https://www.facebook.com/marketplace/item/554720817692660/?%E2%81%A9)
- 2 weeks bond ⁦[https://www.facebook.com/marketplace/item/1108652536739560/?⁩](https://www.facebook.com/marketplace/item/1108652536739560/?%E2%81%A9)
- Bracken ridge ⁦[https://www.facebook.com/marketplace/item/533676729722766/?⁩](https://www.facebook.com/marketplace/item/533676729722766/?%E2%81%A9)
- Morayfield ⁦[https://www.facebook.com/marketplace/item/672255652212091/?⁩](https://www.facebook.com/marketplace/item/672255652212091/?%E2%81%A9)

## Key Responsibilities
<!-- List the key responsibilities in this area -->
-

## Regular Tasks
<!-- Recurring tasks in this area -->
- [ ] Daily: 
- [ ] Weekly: 
	- [ ] Pay rent
	- [ ] tidy up
	- [ ] make a dinner
- [ ] Monthly:
	- [ ] look for backup rooms
		- [ ] check in with the renter and see how things are for them with me living there 

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE area = "Personal" AND (status = "active" OR !contains(status, "completed"))
SORT priority ASC, deadline ASC
```

## Related Resources
```dataview
TABLE WITHOUT ID
  file.link as "Resource",
  source as "Source",
  file.mtime as "Last Modified"
FROM "3-Resources"
WHERE contains(file.content, "[[Rooms for Rent - to enquire]]") OR area = "Personal"
SORT file.mtime DESC
LIMIT 10
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  file.mtime as "Last Modified"
FROM -"Templates"
WHERE contains(file.content, "[[Rooms for Rent - to enquire]]")
SORT file.mtime DESC
LIMIT 5
```

## Key Metrics
<!-- Metrics to track in this area -->
-

## Quick Links
- [[Rooms for Rent - to enquire Project|New Project]]
- [[Rooms for Rent - to enquire Resource|New Resource]]
- [[Rooms for Rent - to enquire Meeting|New Meeting]]
- [[2-Areas|All Areas]]

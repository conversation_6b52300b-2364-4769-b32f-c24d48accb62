---
creation_date: 2025-04-25
modification_date: 2025-04-25
type: area
status: active
area: Security
area_owner: Jordan
responsibility_level: medium
review_frequency: monthly
tags:
  - para/areas
  - Privacy
  - Security
  - PaceySpace
  - Administration
related: 
organization: PaceySpace
key_contacts: 
last_review_date: 2025-04-25
next_review_date: 2025-05-25
---

# Untitled

## Overview
<!-- Brief description of this area of responsibility -->
Tools and clients that respect privacy.
## Current Focus
<!-- What's the current focus in this area? -->
- Need to change email client from Zoho due to privacy concerns. They have stated that they may share with marketing 
- checkout these websites to investigate beeter tools to use, decreasing reliance on big tech products for data security
- https://incogni.com/about-us to remove sensitive data on the web

"You can have a look at this subreddit website : https://www.privacytools.io you’ll find very interesting about good alternatives to Google and Microsoft products. Also very clear explanations, you’ll learn a lot.

If you want to be check other options (less clear explanations than PrivacyTools) : https://prism-break.org

You can also have a look to https://alternativeto.net where you can type the name of something (for example « gmail ») and it will give you a list of alternative programs. Keep in mind this list is based on what people have submitted and what people have voted up or down... so it’s not perfect and may sometimes give bad recommendations. And it’s not specialized in giving you privacy respecting services as this is not their focus. For that nothing beats privacytools.io.

You’ll see that for your question #nextcloud will be what you’re going to find the most in terms of answers as a replacement for gsuite. For a good reason : you can self host it easily ( if you have no clue how to do it and are afraid to break something doing it you can look at https://ownyourbits.com/nextcloudpi/ it just works on any computer with Debian or RaspberryPi and very easy ) or you can rent a Nextcloud instance from a provider. For example well known providers : Hetzner https://www.hetzner.com/storage/storage-share very cheap and works perfectly well. You also have Woelkli https://woelkli.com/en ( more expensive but excellent provider ) and a bunch of other ones at different prices in between. 1 little thing to know : a lot of them offer a free account, for example Woelkli offer 1Go of storage for the free account... but... little tip : if you sign-in to Woelkli from within the Nextcloud app on mobile then it gives you 2 times more free storage. 2Go ! Good news. I’m pretty sure the other official providers may do that too, never tried.

To come back to Zoho : is it good ? Is it bad ? It’s less worse than Google for sure. It’s not perfect. But if you have to start somewhere... Zoho is a very good first step toward the right direction : away from Google. I started by Zoho a long time ago when I wanted to try something else than Google. And today I don’t have a Google account anymore. Hope you’ll find what works the best for you."
https://www.reddit.com/r/privacytoolsIO/comments/guy3tj/how_good_is_zoho_mail_its_apps_for_privacy/

## Key Responsibilities
<!-- List the key responsibilities in this area -->
- 

## Regular Tasks
<!-- Recurring tasks in this area -->
### Daily
- [ ] 

### Weekly
- [ ] 

### Monthly
- [ ] 

### Quarterly
- [ ] 

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE area = "Security" AND (status = "active" OR !contains(status, "completed"))
SORT priority ASC, deadline ASC
```

## Related Resources
```dataview
TABLE WITHOUT ID
  file.link as "Resource",
  source as "Source",
  file.mtime as "Last Modified"
FROM "3-Resources"
WHERE area = "Security"
SORT file.mtime DESC
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  file.mtime as "Last Modified"
FROM -"Templates"
WHERE contains(file.content, "[[Untitled]]")
SORT file.mtime DESC
LIMIT 5
```

## Key Metrics
<!-- Metrics to track in this area -->
- 

## Create Related Notes
- [[Untitled Project|Create New Project]]
- [[Untitled Resource|Create New Resource]]
- [[Untitled Meeting 2025-04-25|Create Meeting Note]]

## Related
- [[2-Areas]]
- [[Areas TOC]]
- [[Security TOC]]

/**
 * Gallery V2 Integration Styles for Main Website
 * Ensures Gallery V2 components work seamlessly within the main website design
 */

/* ===============================
   MAIN WEBSITE INTEGRATION
   =============================== */

/* Gallery V2 containers within main website sections */
.gallery-v2-container {
    width: 100%;
    margin: 2rem 0;
}

/* Ensure Gallery V2 grids work within existing sections */
.category-section .gallery-grid-v2,
.gallery .gallery-grid-v2 {
    margin-top: 2rem;
    margin-bottom: 2rem;
}

/* Adjust Gallery V2 items for main website color scheme */
.gallery-item-v2 {
    background: #ffffff;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.gallery-item-v2:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #007bff;
}

/* Gallery item details styling to match main website */
.gallery-item-details {
    padding: 1rem;
}

.gallery-item-title {
    font-family: 'Playfair Display', serif;
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.gallery-item-meta {
    font-family: 'Raleway', sans-serif;
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

.gallery-item-description {
    font-family: 'Raleway', sans-serif;
    font-size: 0.85rem;
    color: #495057;
    line-height: 1.4;
}

/* Action buttons styling */
.gallery-item-actions .btn-view,
.gallery-item-actions .btn-info {
    background: rgba(0, 123, 255, 0.9);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    margin: 0 0.25rem;
}

.gallery-item-actions .btn-view:hover,
.gallery-item-actions .btn-info:hover {
    background: rgba(0, 123, 255, 1);
    transform: scale(1.1);
}

/* Pagination styling to match main website */
.gallery-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin: 2rem 0;
    font-family: 'Raleway', sans-serif;
}

.gallery-pagination .btn-page {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    height: 40px;
}

.gallery-pagination .btn-page:hover:not(:disabled) {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.gallery-pagination .btn-page:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.gallery-pagination .page-info {
    font-weight: 500;
    color: #495057;
    margin: 0 1rem;
}

/* Loading states */
.loading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    text-align: center;
    color: #6c757d;
    font-family: 'Raleway', sans-serif;
}

.loading-spinner {
    margin-bottom: 1rem;
}

.loading-placeholder p {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #495057;
}

.loading-placeholder small {
    font-size: 0.9rem;
    color: #6c757d;
}

/* Error states */
.gallery-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    text-align: center;
    color: #dc3545;
    font-family: 'Raleway', sans-serif;
}

.gallery-error .error-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.gallery-error .error-message {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 1rem;
    color: #495057;
}

.gallery-error .btn-retry {
    background: #007bff;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.375rem;
    cursor: pointer;
    font-family: 'Raleway', sans-serif;
    font-weight: 500;
    transition: all 0.3s ease;
}

.gallery-error .btn-retry:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

/* Empty gallery state */
.empty-gallery {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    text-align: center;
    color: #6c757d;
    font-family: 'Raleway', sans-serif;
}

.empty-gallery p {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: #495057;
}

.empty-gallery small {
    font-size: 0.9rem;
    color: #6c757d;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .gallery-grid-v2 {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1rem;
    }
    
    .gallery-item-actions .btn-view,
    .gallery-item-actions .btn-info {
        width: 35px;
        height: 35px;
    }
    
    .gallery-pagination {
        gap: 0.25rem;
    }
    
    .gallery-pagination .btn-page {
        min-width: 35px;
        height: 35px;
        padding: 0.25rem 0.5rem;
    }
    
    .gallery-pagination .page-info {
        margin: 0 0.5rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .gallery-grid-v2 {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 0.75rem;
    }
    
    .gallery-item-title {
        font-size: 1rem;
    }
    
    .gallery-item-meta,
    .gallery-item-description {
        font-size: 0.8rem;
    }
    
    .loading-placeholder,
    .gallery-error,
    .empty-gallery {
        padding: 2rem 1rem;
    }
}

/* Integration with existing sort buttons */
.sort-buttons {
    margin-bottom: 1.5rem;
}

.sort-buttons .sort-button.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

/* Ensure proper spacing in category sections */
.category-section .gallery-v2-container {
    margin-top: 1.5rem;
}

/* Performance optimizations for main website integration */
.gallery-v2-container {
    contain: layout style;
}

.gallery-grid-v2 {
    contain: layout style;
}

.gallery-item-v2 {
    contain: layout style paint;
}

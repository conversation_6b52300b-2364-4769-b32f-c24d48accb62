<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Yendor Cats - Exotic Cat Breeder</title>
    <meta name="description" content="Yendor Cats - Exotic and rare cat breeds from a trusted breeder">
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/variables.css">
    <link rel="stylesheet" href="css/gallery-v2.css">
    <link rel="stylesheet" href="css/gallery-v2-integration.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Raleway:wght@300;400;600&display=swap" rel="stylesheet">
    <!-- Font Awesome for Gallery V2 icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg==" crossorigin="anonymous" referrerpolicy="no-referrer">
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="images/favicon.ico">
</head>
<body>
    <!-- Header Section - Expands full height when at the top -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo" id="site-logo">
                    <a href="index.html">
                        <h1>Yendor<span>Cats</span></h1>
                    </a>
                </div>
                <nav class="main-nav">
                    <button class="mobile-menu-toggle" aria-label="Toggle menu">
                        <span></span>
                        <span></span>
                        <span></span>
                    </button>
                    <ul class="nav-list">
                        <li><a href="#home" class="active">Home</a></li>
                        <li><a href="#about">About Us</a></li>
                        <li><a href="#gallery">Our Cats</a></li>
                        <li><a href="#kittens-section">Kittens</a></li>
                        <li><a href="#studs-section">Studs</a></li>
                        <li><a href="#queens-section">Queens</a></li>
                        <li><a href="profiles.html">Search</a></li>
                        <li><a href="upload.html" id="upload-link" style="display: none;">Upload</a></li>
                        <li><a href="#newsletter-signup" class="btn-primary">Get Kitten Updates</a></li>
                        <li><a href="admin.html" class="login-link">Login</a></li>
                    </ul>
                </nav>
            </div>
        </div>
        <!-- Scroll indicator will be added by JavaScript -->
    </header>

    <!-- Main Content - Starts after full-height navbar -->
    <main>
        <!-- Hero Section -->
        <section class="hero" id="home">
            <div class="hero-overlay"></div>
            <div class="container">
                <div class="hero-content">
                    <h2>Maine Coon Cats</h2>
                    <p>The gentle giants of the cat world, bred with love and exceptional care</p>
                    <div class="hero-buttons">
                        <a href="#studs-section" class="btn btn-primary">VIEW OUR CATS</a>
                        <a href="#contact" class="btn btn-secondary">CONTACT US</a>
                    </div>
                </div>
            </div>
        </section>

        <!-- About Section -->
        <section class="about section-padding" id="about">
            <div class="container">
                <div class="section-header">
                    <h2>About Yendor Cats</h2>
                </div>
                <div class="about-content">
                    <div class="about-image">
                        <img src="images/intro_image.jpg" alt="Cat breeder with cats">
                    </div>
                    <div class="about-text">
                        <p>Welcome to Yendor Cats, where our passion for Maine Coon felines is reflected in every cat we raise. We are a family-owned cattery dedicated to raising healthy, well-tempered cats.</p>
                        <p>Our breeding program focuses on maintaining the distinctive traits of each breed while ensuring our cats have sound health and outstanding personalities. See our cat ancestry below.</p>
                        <p>At Yendor Cats, we believe in responsible breeding practices, ensuring all our cats receive the best nutrition, regular veterinary care, and a loving environment.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Introduction Section -->
        <section class="introduction section-padding" id="about-maine-coons">
            <div class="container">
                <div class="section-header">
                    <h2>Yendor Maine Coons</h2>
                </div>
                <div class="introduction-content">
                    <div class="intro-text">
                        <p>I fell in love with this breed several years ago when I bought my first longhaired cat Koriki Sahale. I showed Harley for many years as a very beautiful desexed male to mine and everyone's enjoyment. He reached the status of Gold Double Grand Champion but sadly in June 2012 he passed away unexpectedly.</p>
                        
                        <p>The Maine Coon is a fun-loving, big, gentle cat who loves the company of people. They also love playing with water, other cats and even dogs. An adult male Maine Coon could weigh as much as 10-15kg. A female is much smaller and would probably only get to around 8-9kg and they all take up to 4 years to mature to this size.</p>
                        
                        <p>Maine Coons are very popular all over the world and are doing extremely well on the show bench. They are a natural cat from the state of Maine in the USA, having found their way to the Europe and Australasia where they are also becoming a very popular breed of cat. They are the "Gentle Giants" of the cat world and the very best companion you can have and most of all I enjoy breeding these wonderful big cats.</p>
                        
                        <a href="#" class="history-link" id="show-legends">Learn about Maine Coon legends and history</a>
                    </div>
                    <div class="intro-image">
                        <img src="images/maine_coon1.jpg" alt="Maine Coon Cat">
                        <img src="images/maine_coon2.jpg" alt="Maine Coon Cat">
                    </div>
                </div>
            </div>
        </section>

        <!-- Popup for Maine Coon Legends -->
        <div class="popup-overlay" id="legends-popup">
            <div class="popup-content">
                <span class="close-popup">&times;</span>
                <h2>Legends of the Maine Coon Cat</h2>
                
                <h3>Legends and Lore</h3>
                <p>The Maine Coon is considered a native American breed that occurred naturally in the north eastern areas of the United States and in north eastern Canada. There have been all sorts of stories circulated about this most natural of distinctive cat breeds.</p>
                
                <p>Early legends has it that this robust cat – with its feral appearance, imposing size and bushy tail often marked with rings – resulted from matings between a domestic cat and an American lynx and even more ridiculous, that the first sire was a raccoon. Although genetically impossible, this legend may explain the origin of the breed's name.</p>
                
                <p>Another popular legend possibly explaining the name, tells of a Captain Coon who brought the Maine Coon's ancestors to this country from China.</p>
                
                <p>Surprisingly, history has lined the French Queen Marie Antoinette with the coast of Maine. Prior to her unfortunate demise, the queen planned with the help of an American sea captain, Stephen Clough, to escape to the port of Wiscasset, Maine. A house was set aside for her and her furniture taken there, but the queen never made it to the New World. As the tale goes Captain Clough made the journey, taking along the queen's favourite longhaired cats. Some Maine Coon historians believe today's cats descended from these noble cats.</p>
                
                <p>Another popular scenario traces the Maine Coon's ancestors to the hearty cats brought to America by the Vikings as guardians of their ship's stores.</p>
                
                <p>These cats closely resembled a sturdy breed from Norway called the Skogkatt, whose coat and facial features are similar to the Maine Coon cats'. Like their shipmates, the seafaring cats occasionally jumped ship. Before long it is believed they mated with the native cats of America, and the modern Maine Coon began to evolve. Lacking though, is the documentary evidence of early 17th century European settlers stating that they actually saw domestic type felines when they first arrived.</p>
                
                <div class="popup-images">
                    <img src="resources/maine-coon-history1.jpg" alt="Historical Maine Coon">
                    <img src="resources/maine-coon-history2.jpg" alt="Maine Coon Evolution">
                </div>
            </div>
        </div>

        <!-- Gallery Section -->
        <section class="gallery section-padding" id="gallery">
            <div class="container">
                <div class="section-header">
                    <h2>Our Cats</h2>
                    <p>Let me introduce you to our Maine Coons</p>
                    <button class="view-all-button" data-category="gallery">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                            <circle cx="8.5" cy="8.5" r="1.5"></circle>
                            <polyline points="21 15 16 10 5 21"></polyline>
                        </svg>
                        View All Photos
                    </button>
                </div>
                <div class="gallery-v2-container" data-category="gallery">
                    <!-- Gallery V2 Grid -->
                    <div class="gallery-grid-v2" id="gallery-grid">
                        <div class="loading-placeholder" id="gallery-loading">
                            <div class="loading-spinner">
                                <div class="spinner"></div>
                            </div>
                            <p>Loading high-performance gallery...</p>
                            <small>Optimizing images and warming cache</small>
                        </div>
                    </div>

                    <!-- Gallery pagination -->
                    <div class="gallery-pagination" id="gallery-pagination" style="display: none;">
                        <button class="btn-page" data-action="first" disabled>
                            <i class="fas fa-angle-double-left"></i>
                        </button>
                        <button class="btn-page" data-action="prev" disabled>
                            <i class="fas fa-angle-left"></i>
                        </button>
                        <span class="page-info">Page 1 of 1</span>
                        <button class="btn-page" data-action="next" disabled>
                            <i class="fas fa-angle-right"></i>
                        </button>
                        <button class="btn-page" data-action="last" disabled>
                            <i class="fas fa-angle-double-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Kittens Section -->
        <section class="category-section" id="kittens-section">
            <div class="container">
                <h2>Kitten Announcements</h2>
                <p><h3>Next litters due March 2025</h3>
                  <p>For information on parents to be and to get on list, <a href="#contact">Contact Margaret of Yendor cats.</a></p>
                    <p>Before leaving at around the age of 11/12 weeks old, all kittens are:</p>
                    <ul>
                      <li>desexed</li>
                      <li>micro chipped</li>
                      <li>vaccinated twice</li>
                      <li>socialised</li>
                      <li>have started toilet (litter box) training</li>
                      <li>All my kittens will come with pedigree papers and will be health checked by a vet before leaving me</li>
                    </ul>
                    <p>No kittens leave here until they are fully paid for and desexed.</p>
                    <p>Only sell entire kittens/cats to another registered breeder.</p>
                    <p>Enquiries made through <a href="#contact">Margaret of Yendor Cats</a></p>
                    <a href="#contact" class="btn btn-primary">Get In Touch</a>
                <br />
                <h5>Our adorable Maine Coon Kittens</h4>
                
                <!-- Sorting controls -->
                <div class="sort-buttons">
                    <span>Sort by:</span>
                    <button class="sort-button active" data-category="kittens" data-order-by="date" data-descending="true" data-target="kittens-carousel">Newest</button>
                    <button class="sort-button" data-category="kittens" data-order-by="name" data-descending="false" data-target="kittens-carousel">Name A-Z</button>
                    <button class="sort-button" data-category="kittens" data-order-by="age" data-descending="false" data-target="kittens-carousel">Age (Youngest)</button>
                    <button class="view-all-button" data-category="kittens">
                        <svg xmlns="http://www.w4.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="4" y="3" width="18" height="18" rx="2" ry="2"></rect>
                            <circle cx="9.5" cy="8.5" r="1.5"></circle>
                            <polyline points="22 15 16 10 5 21"></polyline>
                        </svg>
                        View All
                    </button>
                </div>
                
                <div class="gallery-v2-container" data-category="kittens">
                    <!-- Kittens Gallery V2 Grid -->
                    <div class="gallery-grid-v2" id="kittens-gallery">
                        <div class="loading-placeholder" id="kittens-loading">
                            <div class="loading-spinner">
                                <div class="spinner"></div>
                            </div>
                            <p>Loading adorable kittens...</p>
                            <small>Fetching from Backblaze storage</small>
                        </div>
                    </div>

                    <!-- Kittens pagination -->
                    <div class="gallery-pagination" id="kittens-pagination" style="display: none;">
                        <button class="btn-page" data-action="first" disabled>
                            <i class="fas fa-angle-double-left"></i>
                        </button>
                        <button class="btn-page" data-action="prev" disabled>
                            <i class="fas fa-angle-left"></i>
                        </button>
                        <span class="page-info">Page 1 of 1</span>
                        <button class="btn-page" data-action="next" disabled>
                            <i class="fas fa-angle-right"></i>
                        </button>
                        <button class="btn-page" data-action="last" disabled>
                            <i class="fas fa-angle-double-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Studs Section -->
        <section class="category-section" id="studs-section">
            <div class="container">
                <h2>Studs</h2>
                <p class="section-intro">Meet our magnificent male Maine Coons who father our beautiful kittens.</p>
                
                <!-- Sorting controls -->
                <div class="sort-buttons">
                    <span>Sort by:</span>
                    <button class="sort-button active" data-category="studs" data-order-by="date" data-descending="true" data-target="studs-carousel">Newest</button>
                    <button class="sort-button" data-category="studs" data-order-by="name" data-descending="false" data-target="studs-carousel">Name A-Z</button>
                    <button class="sort-button" data-category="studs" data-order-by="age" data-descending="false" data-target="studs-carousel">Age (Youngest)</button>
                    <button class="view-all-button" data-category="studs">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                            <circle cx="8.5" cy="8.5" r="1.5"></circle>
                            <polyline points="21 15 16 10 5 21"></polyline>
                        </svg>
                        View All
                    </button>
                </div>
                
                <div class="gallery-v2-container" data-category="studs">
                    <!-- Studs Gallery V2 Grid -->
                    <div class="gallery-grid-v2" id="studs-gallery">
                        <div class="loading-placeholder" id="studs-loading">
                            <div class="loading-spinner">
                                <div class="spinner"></div>
                            </div>
                            <p>Loading magnificent studs...</p>
                            <small>Fetching from Backblaze storage</small>
                        </div>
                    </div>

                    <!-- Studs pagination -->
                    <div class="gallery-pagination" id="studs-pagination" style="display: none;">
                        <button class="btn-page" data-action="first" disabled>
                            <i class="fas fa-angle-double-left"></i>
                        </button>
                        <button class="btn-page" data-action="prev" disabled>
                            <i class="fas fa-angle-left"></i>
                        </button>
                        <span class="page-info">Page 1 of 1</span>
                        <button class="btn-page" data-action="next" disabled>
                            <i class="fas fa-angle-right"></i>
                        </button>
                        <button class="btn-page" data-action="last" disabled>
                            <i class="fas fa-angle-double-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Queens Section -->
        <section class="category-section" id="queens-section">
            <div class="container">
                <h2>Queens</h2>
                <p class="section-intro">Our beautiful female Maine Coons who bring our kittens into the world.</p>
                
                <!-- Sorting controls -->
                <div class="sort-buttons">
                    <span>Sort by:</span>
                    <button class="sort-button active" data-category="queens" data-order-by="date" data-descending="true" data-target="queens-carousel">Newest</button>
                    <button class="sort-button" data-category="queens" data-order-by="name" data-descending="false" data-target="queens-carousel">Name A-Z</button>
                    <button class="sort-button" data-category="queens" data-order-by="age" data-descending="false" data-target="queens-carousel">Age (Youngest)</button>
                    <button class="view-all-button" data-category="queens">
                        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                            <circle cx="8.5" cy="8.5" r="1.5"></circle>
                            <polyline points="21 15 16 10 5 21"></polyline>
                        </svg>
                        View All
                    </button>
                </div>
                
                <div class="gallery-v2-container" data-category="queens">
                    <!-- Queens Gallery V2 Grid -->
                    <div class="gallery-grid-v2" id="queens-gallery">
                        <div class="loading-placeholder" id="queens-loading">
                            <div class="loading-spinner">
                                <div class="spinner"></div>
                            </div>
                            <p>Loading beautiful queens...</p>
                            <small>Fetching from Backblaze storage</small>
                        </div>
                    </div>

                    <!-- Queens pagination -->
                    <div class="gallery-pagination" id="queens-pagination" style="display: none;">
                        <button class="btn-page" data-action="first" disabled>
                            <i class="fas fa-angle-double-left"></i>
                        </button>
                        <button class="btn-page" data-action="prev" disabled>
                            <i class="fas fa-angle-left"></i>
                        </button>
                        <span class="page-info">Page 1 of 1</span>
                        <button class="btn-page" data-action="next" disabled>
                            <i class="fas fa-angle-right"></i>
                        </button>
                        <button class="btn-page" data-action="last" disabled>
                            <i class="fas fa-angle-double-right"></i>
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- Newsletter Signup Section -->
        <section class="newsletter-signup section-padding" id="newsletter-signup">
            <div class="container">
                <div class="section-header">
                    <h2>Stay Updated on Kitten Availability</h2>
                    <p>Be the first to know when new Maine Coon kittens are available! Join our mailing list for exclusive updates on upcoming litters, kitten announcements, and special news from Yendor Cats.</p>
                </div>
                <div class="newsletter-content">
                    <div class="newsletter-benefits">
                        <h3>What You'll Receive:</h3>
                        <ul>
                            <li>🐱 Early notifications about new litters</li>
                            <li>📸 Exclusive kitten photos and updates</li>
                            <li>📅 Important dates and availability windows</li>
                            <li>💝 Special announcements and cattery news</li>
                        </ul>
                    </div>
                    <div class="newsletter-form">
                        <form id="newsletter-form" action="#" method="post">
                            <div class="form-group">
                                <label for="newsletter-name">Your Name</label>
                                <input type="text" id="newsletter-name" name="name" required>
                            </div>
                            <div class="form-group">
                                <label for="newsletter-email">Email Address</label>
                                <input type="email" id="newsletter-email" name="email" required>
                            </div>
                            <div class="form-group">
                                <label for="newsletter-interests">Interested In (Optional)</label>
                                <select id="newsletter-interests" name="interests">
                                    <option value="">Select your interest</option>
                                    <option value="kittens">Available Kittens</option>
                                    <option value="breeding">Breeding Information</option>
                                    <option value="shows">Cat Shows & Events</option>
                                    <option value="all">All Updates</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary newsletter-submit">
                                Subscribe to Updates
                            </button>
                        </form>
                        <p class="newsletter-privacy">
                            <small>We respect your privacy. Unsubscribe at any time. We'll never share your information.</small>
                        </p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section class="contact section-padding" id="contact">
            <div class="container">
                <div class="section-header">
                    <h2>Contact Us</h2>
                    <p>Have questions or want to arrange a visit? Get in touch with us</p>
                    <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <p>Phone: <a href="tel:0417281675">0417281675</a></p>
                </div>
                <div class="contact-container">
                    <!-- Contact form would go here -->
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h3>Yendor<span>Cats</span></h3>
                </div>
                <div class="footer-info">
                    <p>&copy; 2024 Yendor Cats. All Rights Reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <a href="#" class="back-to-top" aria-label="Back to top">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="18 15 12 9 6 15"></polyline>
        </svg>
    </a>

    <!-- Thumbnail Gallery Popup -->
    <div class="thumbnail-gallery-popup">
        <div class="thumbnail-gallery-header">
            <h2 class="thumbnail-gallery-title">All Photos</h2>
            <button class="thumbnail-gallery-close" aria-label="Close gallery">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
        </div>
        <div class="thumbnail-gallery-container">
            <div class="thumbnail-grid">
                <!-- Thumbnails will be dynamically loaded here -->
                <div class="loading-placeholder">
                    <div class="spinner"></div>
                    <p>Loading images...</p>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/metadata-sync.js"></script>
    <script src="js/navbar.js"></script>
    <script src="js/gallery-v2.js"></script>
    <script src="js/gallery-v2-integration.js"></script>
    <script src="js/main.js"></script>
</body>
</html>

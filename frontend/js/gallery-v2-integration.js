/**
 * Gallery V2 Integration for Main Website
 * Integrates the high-performance Gallery V2 system into the main Yendor Cats website
 * Replaces the carousel system with advanced caching and Backblaze storage support
 */

class MainWebsiteGalleryV2 {
    constructor() {
        this.galleries = new Map();
        this.currentLightboxImages = [];
        this.currentLightboxIndex = 0;
        this.init();
    }

    /**
     * Initialize Gallery V2 for main website
     */
    init() {
        console.log('🚀 Initializing Gallery V2 integration for main website');
        
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setupGalleries());
        } else {
            this.setupGalleries();
        }
    }

    /**
     * Setup all gallery sections
     */
    async setupGalleries() {
        const galleryConfigs = [
            {
                category: 'gallery',
                containerId: 'gallery-grid',
                paginationId: 'gallery-pagination',
                loadingId: 'gallery-loading',
                sortButtons: '.sort-button[data-category="gallery"]'
            },
            {
                category: 'kittens',
                containerId: 'kittens-gallery',
                paginationId: 'kittens-pagination',
                loadingId: 'kittens-loading',
                sortButtons: '.sort-button[data-category="kittens"]'
            },
            {
                category: 'studs',
                containerId: 'studs-gallery',
                paginationId: 'studs-pagination',
                loadingId: 'studs-loading',
                sortButtons: '.sort-button[data-category="studs"]'
            },
            {
                category: 'queens',
                containerId: 'queens-gallery',
                paginationId: 'queens-pagination',
                loadingId: 'queens-loading',
                sortButtons: '.sort-button[data-category="queens"]'
            }
        ];

        // Initialize each gallery with staggered loading
        for (let i = 0; i < galleryConfigs.length; i++) {
            const config = galleryConfigs[i];
            
            // Stagger initialization to prevent overwhelming the API
            setTimeout(() => {
                this.initializeGallery(config);
            }, i * 500);
        }

        // Setup lightbox
        this.setupLightbox();
        
        // Setup view all buttons
        this.setupViewAllButtons();
    }

    /**
     * Initialize a single gallery section
     */
    async initializeGallery(config) {
        try {
            console.log(`📸 Initializing ${config.category} gallery`);

            // Create Gallery V2 instance with proper API configuration
            const gallery = new GalleryV2({
                baseUrl: '/api/v2/gallery',
                fallbackUrl: '/api/PublicGallery/category',
                cacheExpiry: 5 * 60 * 1000, // 5 minutes
                localStorageExpiry: 30 * 60 * 1000, // 30 minutes
                lazyLoadThreshold: 200,
                preloadCount: 3,
                enablePerformanceMonitoring: true,
                retryAttempts: 3,
                retryDelay: 1000
            });

            // Store gallery instance
            this.galleries.set(config.category, {
                instance: gallery,
                config: config,
                currentPage: 1,
                currentSort: 'DateTaken',
                currentDescending: true
            });

            // Setup event listeners
            gallery.on('gallery:loaded', (data) => {
                this.handleGalleryLoaded(config.category, data);
            });

            gallery.on('image:view', (data) => {
                this.showLightbox(data.item, config.category);
            });

            gallery.on('performance:metrics', (metrics) => {
                console.log(`📊 ${config.category} performance:`, metrics);
            });

            // Setup sort button handlers
            this.setupSortButtons(config);

            // Load initial data
            await this.loadGalleryData(config.category);

        } catch (error) {
            console.error(`❌ Error initializing ${config.category} gallery:`, error);
            this.showError(config.containerId, `Failed to load ${config.category} gallery`);
        }
    }

    /**
     * Load gallery data for a category
     */
    async loadGalleryData(category, page = 1, sortBy = 'DateTaken', descending = true) {
        const galleryData = this.galleries.get(category);
        if (!galleryData) return;

        const { instance, config } = galleryData;
        
        try {
            console.log(`🔄 Loading ${category} data - Page ${page}, Sort: ${sortBy}`);
            
            // Show loading state
            this.showLoading(config.loadingId, true);
            
            // Load data using Gallery V2
            const data = await instance.loadCategory(category, page, 20, sortBy, descending, true);
            
            // Update gallery state
            galleryData.currentPage = page;
            galleryData.currentSort = sortBy;
            galleryData.currentDescending = descending;
            
            // Render the gallery
            this.renderGallery(category, data);
            
        } catch (error) {
            console.error(`❌ Error loading ${category} data:`, error);
            this.showError(config.containerId, `Failed to load ${category} images`);
        } finally {
            this.showLoading(config.loadingId, false);
        }
    }

    /**
     * Render gallery data
     */
    renderGallery(category, data) {
        const galleryData = this.galleries.get(category);
        if (!galleryData) return;

        const { config } = galleryData;
        const container = document.getElementById(config.containerId);
        if (!container) return;

        // Clear existing content
        container.innerHTML = '';

        if (!data.items || data.items.length === 0) {
            container.innerHTML = `
                <div class="empty-gallery">
                    <p>No ${category} images found.</p>
                    <small>Images may still be loading from Backblaze storage.</small>
                </div>
            `;
            return;
        }

        // Create gallery items
        data.items.forEach((item, index) => {
            const galleryItem = this.createGalleryItem(item, category, index);
            container.appendChild(galleryItem);
        });

        // Update pagination
        this.updatePagination(category, data);

        // Setup lazy loading
        this.setupLazyLoading(container);

        console.log(`✅ Rendered ${data.items.length} ${category} images`);
    }

    /**
     * Create a gallery item element
     */
    createGalleryItem(item, category, index) {
        const article = document.createElement('article');
        article.className = 'gallery-item-v2';
        article.setAttribute('data-id', item.id);
        article.setAttribute('data-category', category);
        article.setAttribute('data-index', index);

        const displayName = item.title || item.catName || 'Maine Coon Cat';
        const imageUrl = item.imageUrl || item.publicUrl;
        const thumbnailUrl = item.thumbnailUrl || imageUrl;

        article.innerHTML = `
            <div class="gallery-item-media">
                <img 
                    class="gallery-image lazy-load" 
                    data-src="${imageUrl}"
                    data-thumbnail="${thumbnailUrl}"
                    alt="${displayName}"
                    loading="lazy"
                    style="aspect-ratio: ${item.aspectRatio || 1}; width: 100%; height: auto;"
                />
                <div class="gallery-item-overlay">
                    <div class="gallery-item-actions">
                        <button class="btn-view" data-id="${item.id}" title="View Full Size">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn-info" data-id="${item.id}" title="View Details">
                            <i class="fas fa-info-circle"></i>
                        </button>
                    </div>
                </div>
                <div class="gallery-item-loading">
                    <div class="spinner"></div>
                </div>
            </div>
            <div class="gallery-item-details">
                <h3 class="gallery-item-title">${displayName}</h3>
                <div class="gallery-item-meta">
                    <span class="cat-info">
                        ${item.breed || 'Maine Coon'}
                        ${item.bloodline ? ` • ${item.bloodline}` : ''}
                        ${item.gender ? ` • ${item.gender}` : ''}
                    </span>
                    ${item.ageAtPhoto ? `<span class="age-info">${item.ageAtPhoto}</span>` : ''}
                    ${item.dateTaken ? `<span class="date-info">${new Date(item.dateTaken).toLocaleDateString()}</span>` : ''}
                </div>
                ${item.description ? `<p class="gallery-item-description">${item.description}</p>` : ''}
            </div>
        `;

        // Add click handlers
        this.setupItemHandlers(article, item, category);

        return article;
    }

    /**
     * Setup item event handlers
     */
    setupItemHandlers(article, item, category) {
        // View button
        const viewBtn = article.querySelector('.btn-view');
        if (viewBtn) {
            viewBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.showLightbox(item, category);
            });
        }

        // Info button
        const infoBtn = article.querySelector('.btn-info');
        if (infoBtn) {
            infoBtn.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.showImageDetails(item);
            });
        }

        // Image click
        const img = article.querySelector('.gallery-image');
        if (img) {
            img.addEventListener('click', (e) => {
                e.preventDefault();
                this.showLightbox(item, category);
            });
        }
    }

    /**
     * Handle gallery loaded event
     */
    handleGalleryLoaded(category, data) {
        console.log(`✅ ${category} gallery loaded:`, data);
        
        // Update any UI elements that show gallery stats
        const statsElement = document.querySelector(`#${category}-stats`);
        if (statsElement) {
            statsElement.innerHTML = `
                <span>Images: ${data.data.totalCount || 0}</span>
                <span>Source: ${data.data.cacheSource || 'unknown'}</span>
                <span>Load time: ${(data.data.queryTime || 0).toFixed(0)}ms</span>
            `;
        }
    }

    /**
     * Show loading state
     */
    showLoading(loadingId, show) {
        const loadingElement = document.getElementById(loadingId);
        if (loadingElement) {
            loadingElement.style.display = show ? 'block' : 'none';
        }
    }

    /**
     * Show error message
     */
    showError(containerId, message) {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = `
                <div class="gallery-error">
                    <div class="error-icon">⚠️</div>
                    <div class="error-message">${message}</div>
                    <button class="btn-retry" onclick="location.reload()">Retry</button>
                </div>
            `;
        }
    }

    /**
     * Setup sort button handlers
     */
    setupSortButtons(config) {
        const sortButtons = document.querySelectorAll(config.sortButtons);
        sortButtons.forEach(button => {
            button.addEventListener('click', async (e) => {
                e.preventDefault();

                // Update active state
                sortButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');

                // Get sort parameters
                const orderBy = button.dataset.orderBy || 'DateTaken';
                const descending = button.dataset.descending === 'true';

                // Reload gallery with new sort
                await this.loadGalleryData(config.category, 1, orderBy, descending);
            });
        });
    }

    /**
     * Setup view all buttons
     */
    setupViewAllButtons() {
        const viewAllButtons = document.querySelectorAll('.view-all-button');
        viewAllButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                const category = button.dataset.category;
                this.openThumbnailGallery(category);
            });
        });
    }

    /**
     * Setup lazy loading for images
     */
    setupLazyLoading(container) {
        const lazyImages = container.querySelectorAll('.lazy-load');

        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        this.loadImage(img);
                        imageObserver.unobserve(img);
                    }
                });
            }, {
                rootMargin: '200px'
            });

            lazyImages.forEach(img => imageObserver.observe(img));
        } else {
            // Fallback for older browsers
            lazyImages.forEach(img => this.loadImage(img));
        }
    }

    /**
     * Load individual image
     */
    loadImage(imgElement) {
        const src = imgElement.dataset.src;
        const thumbnail = imgElement.dataset.thumbnail;

        if (!src) return;

        // Show loading state
        const loadingDiv = imgElement.closest('.gallery-item-v2').querySelector('.gallery-item-loading');
        if (loadingDiv) {
            loadingDiv.style.display = 'block';
        }

        // Create new image to preload
        const img = new Image();

        img.onload = () => {
            imgElement.src = src;
            imgElement.classList.add('loaded');

            if (loadingDiv) {
                loadingDiv.style.display = 'none';
            }
        };

        img.onerror = () => {
            console.warn('Failed to load image:', src);

            // Try thumbnail as fallback
            if (thumbnail && thumbnail !== src) {
                imgElement.src = thumbnail;
                imgElement.classList.add('fallback');
            } else {
                imgElement.classList.add('error');
                imgElement.alt = 'Failed to load image';
            }

            if (loadingDiv) {
                loadingDiv.style.display = 'none';
            }
        };

        img.src = src;
    }

    /**
     * Update pagination
     */
    updatePagination(category, data) {
        const galleryData = this.galleries.get(category);
        if (!galleryData) return;

        const paginationElement = document.getElementById(galleryData.config.paginationId);
        if (!paginationElement) return;

        const { page, totalPages, hasNext, hasPrevious } = data;

        if (totalPages <= 1) {
            paginationElement.style.display = 'none';
            return;
        }

        paginationElement.style.display = 'flex';
        paginationElement.innerHTML = `
            <button class="btn-page" data-action="first" ${!hasPrevious ? 'disabled' : ''}>
                <i class="fas fa-angle-double-left"></i>
            </button>
            <button class="btn-page" data-action="prev" ${!hasPrevious ? 'disabled' : ''}>
                <i class="fas fa-angle-left"></i>
            </button>
            <span class="page-info">Page ${page} of ${totalPages}</span>
            <button class="btn-page" data-action="next" ${!hasNext ? 'disabled' : ''}>
                <i class="fas fa-angle-right"></i>
            </button>
            <button class="btn-page" data-action="last" ${!hasNext ? 'disabled' : ''}>
                <i class="fas fa-angle-double-right"></i>
            </button>
        `;

        // Add pagination event listeners
        paginationElement.querySelectorAll('.btn-page').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                const action = e.target.closest('.btn-page').dataset.action;
                await this.handlePagination(category, action, { page, totalPages });
            });
        });
    }

    /**
     * Handle pagination actions
     */
    async handlePagination(category, action, { page, totalPages }) {
        const galleryData = this.galleries.get(category);
        if (!galleryData) return;

        let newPage = page;

        switch (action) {
            case 'first':
                newPage = 1;
                break;
            case 'prev':
                newPage = Math.max(1, page - 1);
                break;
            case 'next':
                newPage = Math.min(totalPages, page + 1);
                break;
            case 'last':
                newPage = totalPages;
                break;
        }

        if (newPage !== page) {
            await this.loadGalleryData(
                category,
                newPage,
                galleryData.currentSort,
                galleryData.currentDescending
            );
        }
    }

    /**
     * Show lightbox for image viewing
     */
    showLightbox(item, category) {
        // Implementation will be added in next chunk
        console.log('Lightbox:', item, category);
    }

    /**
     * Show image details
     */
    showImageDetails(item) {
        console.log('Image details:', item);
    }

    /**
     * Open thumbnail gallery popup
     */
    openThumbnailGallery(category) {
        console.log('Open thumbnail gallery:', category);
    }

    /**
     * Setup lightbox functionality
     */
    setupLightbox() {
        // Implementation will be added in next chunk
        console.log('Setting up lightbox...');
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🎯 Starting Gallery V2 integration...');
    window.mainGalleryV2 = new MainWebsiteGalleryV2();
});

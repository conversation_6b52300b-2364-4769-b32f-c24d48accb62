---
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: resource
source: <% await tp.system.suggester(["Personal research", "Website", "Book", "Course", "Documentation", "Video", "Podcast", "Other"], ["Personal research", "Website", "Book", "Course", "Documentation", "Video", "Podcast", await tp.system.prompt("Source")], false, "Source") %>
difficulty: <% await tp.system.suggester(["easy", "medium", "hard", "expert"], ["easy", "medium", "hard", "expert"], false, "Difficulty") %>
area: <% await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church", "University", "Other"], ["Software-Development", "Administration", "Personal", "Church", "University", await tp.system.prompt("Area Name")], false, "Related Area") %>
resource_type: <% await tp.system.suggester(["guide", "reference", "tool", "tutorial", "template", "checklist", "documentation", "other"], ["guide", "reference", "tool", "tutorial", "template", "checklist", "documentation", await tp.system.prompt("Resource Type")], false, "Resource Type") %>
tags: [para/resources, <% await tp.system.suggester(["guide", "reference", "tool", "tutorial", "template", "checklist", "documentation", "software-dev", "church", "personal", "university", "admin", "finance", "compliance", "critical", "urgent", "important"], ["guide", "reference", "tool", "tutorial", "template", "checklist", "documentation", "software-dev", "church", "personal", "university", "admin", "finance", "compliance", "critical", "urgent", "important"], false, "Primary Tag") %>]
url: <% await tp.system.prompt("URL (if applicable)", "") %>
author: <% await tp.system.prompt("Author (if applicable)", "") %>
last_used: <% tp.date.now("YYYY-MM-DD") %>
usefulness_rating: <% await tp.system.suggester(["5", "4", "3", "2", "1"], ["5", "4", "3", "2", "1"], false, "Usefulness Rating (1-5)") %>
keywords: []
related:
  depends-on: []
  blocks: []
  area-overlap: []
  references: []
  supports: []
  relates-to: []
priority_score: 0
---

# <% tp.file.title %>

<%* 
// Auto-calculate resource priority score
const usefulness = parseInt(tp.frontmatter.usefulness_rating) || 3;
const difficulty = tp.frontmatter.difficulty;
const resourceType = tp.frontmatter.resource_type;
const tags = tp.frontmatter.tags || [];
const lastUsed = tp.frontmatter.last_used;

let score = 0;

// Usefulness rating weight (higher rating = higher priority)
score += usefulness * 10;

// Difficulty weight (easier resources are more accessible)
if (difficulty === "easy") score += 15;
else if (difficulty === "medium") score += 10;
else if (difficulty === "hard") score += 5;
else score += 2; // expert

// Resource type weight (some types are more critical)
const typeWeights = {
  "reference": 20,
  "guide": 15,
  "tool": 18,
  "tutorial": 12,
  "template": 16,
  "checklist": 14,
  "documentation": 10
};

if (typeWeights[resourceType]) {
  score += typeWeights[resourceType];
}

// Recently used bonus
if (lastUsed) {
  const usedDate = new Date(lastUsed);
  const today = new Date();
  const diffTime = today.getTime() - usedDate.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  if (diffDays <= 7) score += 15; // Used this week
  else if (diffDays <= 30) score += 10; // Used this month
  else if (diffDays <= 90) score += 5; // Used this quarter
}

// Tag importance weights
const tagWeights = {
  "critical": 25,
  "urgent": 20,
  "important": 15,
  "church": 15,
  "admin": 10,
  "personal": 5,
  "finance": 20,
  "compliance": 25
};

tags.forEach(tag => {
  if (tagWeights[tag]) {
    score += tagWeights[tag];
  }
});

// Update frontmatter with calculated score
const currentFile = tp.file.find_tfile(tp.file.path(true));
if (currentFile) {
  const content = await app.vault.read(currentFile);
  const updatedContent = content.replace(/priority_score: \d+/, `priority_score: ${score}`);
  await app.vault.modify(currentFile, updatedContent);
}
%>

## Overview
<!-- Brief description of this resource -->

## Key Points
<!-- Main takeaways or important information -->
- 

## Details
<!-- Detailed information -->

## Examples
<!-- Examples or code snippets if applicable -->
```
// Code example
```

<%* 
// Smart relationship queries - only show if relationships exist
const related = tp.frontmatter.related || {};
let hasRelated = false;

// Check if any relationship arrays have content
for (const key in related) {
  if (related[key] && related[key].length > 0) {
    hasRelated = true;
    break;
  }
}

if (hasRelated) {
  tR += "\n## 🔗 Relationships\n";
  
  // Resource Dependencies (highest priority)
  if (related["depends-on"] && related["depends-on"].length > 0) {
    tR += "\n<details><summary>🚨 Resource Dependencies (" + related["depends-on"].length + " items)</summary>\n\n";
    tR += "```dataview\n";
    tR += "TABLE WITHOUT ID\n";
    tR += "  file.link as \"Resource\",\n";
    tR += "  resource_type as \"Type\",\n";
    tR += "  difficulty as \"Difficulty\",\n";
    tR += "  usefulness_rating as \"Rating\"\n";
    tR += "FROM \"3-Resources\"\n";
    tR += "WHERE file.name IN [";
    related["depends-on"].forEach((item, index) => {
      const noteName = item.replace(/\[\[|\]\]/g, '');
      tR += "\"" + noteName + "\"";
      if (index < related["depends-on"].length - 1) tR += ", ";
    });
    tR += "]\n";
    tR += "SORT difficulty ASC\n";
    tR += "```\n";
    tR += "</details>\n";
  }
  
  // What this resource supports
  if (related["supports"] && related["supports"].length > 0) {
    tR += "\n<details><summary>⚡ Supports These Items (" + related["supports"].length + " items)</summary>\n\n";
    tR += "```dataview\n";
    tR += "TABLE WITHOUT ID\n";
    tR += "  file.link as \"Note\",\n";
    tR += "  type as \"Type\",\n";
    tR += "  file.mtime as \"Last Modified\"\n";
    tR += "FROM -\"Templates\"\n";
    tR += "WHERE file.name IN [";
    related["supports"].forEach((item, index) => {
      const noteName = item.replace(/\[\[|\]\]/g, '');
      tR += "\"" + noteName + "\"";
      if (index < related["supports"].length - 1) tR += ", ";
    });
    tR += "]\n";
    tR += "SORT type ASC\n";
    tR += "```\n";
    tR += "</details>\n";
  }
  
  // Area overlaps
  if (related["area-overlap"] && related["area-overlap"].length > 0) {
    tR += "\n<details><summary>📊 Area Overlaps (" + related["area-overlap"].length + " items)</summary>\n\n";
    tR += "```dataview\n";
    tR += "TABLE WITHOUT ID\n";
    tR += "  file.link as \"Area\",\n";
    tR += "  responsibility_level as \"Responsibility\",\n";
    tR += "  status as \"Status\"\n";
    tR += "FROM \"2-Areas\"\n";
    tR += "WHERE file.name IN [";
    related["area-overlap"].forEach((item, index) => {
      const noteName = item.replace(/\[\[|\]\]/g, '');
      tR += "\"" + noteName + "\"";
      if (index < related["area-overlap"].length - 1) tR += ", ";
    });
    tR += "]\n";
    tR += "SORT responsibility_level ASC\n";
    tR += "```\n";
    tR += "</details>\n";
  }
  
  // References
  if (related["references"] && related["references"].length > 0) {
    tR += "\n<details><summary>📚 References (" + related["references"].length + " items)</summary>\n\n";
    tR += "```dataview\n";
    tR += "TABLE WITHOUT ID\n";
    tR += "  file.link as \"Resource\",\n";
    tR += "  source as \"Source\",\n";
    tR += "  difficulty as \"Difficulty\"\n";
    tR += "FROM \"3-Resources\"\n";
    tR += "WHERE file.name IN [";
    related["references"].forEach((item, index) => {
      const noteName = item.replace(/\[\[|\]\]/g, '');
      tR += "\"" + noteName + "\"";
      if (index < related["references"].length - 1) tR += ", ";
    });
    tR += "]\n";
    tR += "SORT difficulty ASC\n";
    tR += "```\n";
    tR += "</details>\n";
  }
}
%>

## Related Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE contains(file.content, "[[<% tp.file.title %>]]") OR contains(related.references, "<% tp.file.title %>")
SORT priority ASC
```

## Notes
<!-- Any additional notes -->

<%* 
// Auto-generated "See Also" based on shared tags and area
const currentArea = tp.frontmatter.area;
const currentTags = tp.frontmatter.tags || [];
const currentType = tp.frontmatter.resource_type;

if (currentArea || currentTags.length > 0 || currentType) {
  tR += "\n## 🔍 See Also\n";
  
  // Related resources of same type
  if (currentType) {
    tR += "\n### Similar Resources\n";
    tR += "```dataview\n";
    tR += "TABLE WITHOUT ID\n";
    tR += "  file.link as \"Resource\",\n";
    tR += "  difficulty as \"Difficulty\",\n";
    tR += "  usefulness_rating as \"Rating\",\n";
    tR += "  source as \"Source\"\n";
    tR += "FROM \"3-Resources\"\n";
    tR += "WHERE resource_type = \"" + currentType + "\" AND file.name != \"" + tp.file.title + "\"\n";
    tR += "SORT usefulness_rating DESC\n";
    tR += "LIMIT 5\n";
    tR += "```\n";
  }
  
  // Related resources in same area
  if (currentArea) {
    tR += "\n### Area Resources\n";
    tR += "```dataview\n";
    tR += "TABLE WITHOUT ID\n";
    tR += "  file.link as \"Resource\",\n";
    tR += "  resource_type as \"Type\",\n";
    tR += "  difficulty as \"Difficulty\"\n";
    tR += "FROM \"3-Resources\"\n";
    tR += "WHERE area = \"" + currentArea + "\" AND file.name != \"" + tp.file.title + "\"\n";
    tR += "SORT difficulty ASC\n";
    tR += "LIMIT 5\n";
    tR += "```\n";
  }
  
  // Related resources by tags
  if (currentTags.length > 0) {
    tR += "\n### Related by Tags\n";
    tR += "```dataview\n";
    tR += "TABLE WITHOUT ID\n";
    tR += "  file.link as \"Resource\",\n";
    tR += "  resource_type as \"Type\",\n";
    tR += "  usefulness_rating as \"Rating\"\n";
    tR += "FROM \"3-Resources\"\n";
    tR += "WHERE (";
    currentTags.forEach((tag, index) => {
      tR += "contains(tags, \"" + tag + "\")";
      if (index < currentTags.length - 1) tR += " OR ";
    });
    tR += ") AND file.name != \"" + tp.file.title + "\"\n";
    tR += "SORT usefulness_rating DESC\n";
    tR += "LIMIT 5\n";
    tR += "```\n";
  }
}
%>

## Quick Links
- [[<% tp.file.title %> Project|New Project]]
- [[<% tp.file.title %> Reference|Quick Reference]]
- [[3-Resources|All Resources]]

---
# CORE METADATA
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: area
status: <% await tp.system.suggester(["active", "inactive", "archived"], ["active", "inactive", "archived"], false, "Area Status") %>
priority: <% await tp.system.suggester(["critical", "high", "medium", "low"], ["critical", "high", "medium", "low"], false, "Area Priority") %>
area_category: <% await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church", "Education", "Finance"], ["Software-Development", "Administration", "Personal", "Church", "Education", "Finance"], false, "Area Category") %>
owner: Jordan
tags: [para/areas, <% await tp.system.suggester(["software-dev", "administration", "church", "personal", "education", "finance"], ["software-dev", "administration", "church", "personal", "education", "finance"], false, "Primary Tag") %>]

# AREA MANAGEMENT
responsibility_level: <% await tp.system.suggester(["critical", "high", "medium", "low"], ["critical", "high", "medium", "low"], false, "Responsibility Level") %>
review_frequency: <% await tp.system.suggester(["daily", "weekly", "monthly", "quarterly"], ["daily", "weekly", "monthly", "quarterly"], false, "Review Frequency") %>
last_review_date: <% tp.date.now("YYYY-MM-DD") %>
next_review_date: <% tp.date.now("YYYY-MM-DD", 30) %>
key_metrics: []

# RELATIONSHIPS (Enhanced System)
related_projects: []
related_resources: []
related_people: ["[[Jordan]]"]
depends_on: []

# TASK MANAGEMENT
task_priority: <% await tp.system.suggester(["urgent", "high", "medium", "routine"], ["urgent", "high", "medium", "routine"], false, "Default Task Priority") %>
task_context: <% await tp.system.suggester(["admin", "maintenance", "communication", "deep-work"], ["admin", "maintenance", "communication", "deep-work"], false, "Primary Task Context") %>
---

# <% tp.file.title %>

## Overview
<!-- Brief description of this area of responsibility -->

## Current Focus
<!-- What's the current focus in this area? -->
-

## Key Responsibilities
<!-- List the key responsibilities in this area -->
-

## 📋 This Week's Focus
<%*
const weeklyTaskQuery = `
TABLE WITHOUT ID
  choice(task_priority = "urgent", "🔥", choice(task_priority = "high", "⚡", "📋")) as "P",
  file.link as "This Week's Tasks",
  choice(estimated_time, estimated_time, "?") as "Time",
  choice(deadline, deadline, "Flexible") as "Due"
FROM this.file.folder OR "1-Projects"
WHERE !completed
  AND contains(related_areas, "${tp.file.title}")
  AND due >= date(today)
  AND due <= date(today) + dur(7 days)
SORT task_priority ASC, due ASC
LIMIT 10
`;
tR += "```dataview\n" + weeklyTaskQuery + "\n```\n\n";
%>

## 🔄 Routine Maintenance
<%*
const routineQuery = `
TABLE WITHOUT ID
  "🔄" as "",
  file.link as "Routine Tasks",
  estimated_time as "Time"
FROM this.file.folder
WHERE !completed
  AND task_priority = "routine"
  AND task_context = "maintenance"
SORT estimated_time ASC
LIMIT 5
`;
tR += "```dataview\n" + routineQuery + "\n```\n\n";
%>

## Regular Tasks Checklist
<!-- Recurring tasks in this area -->
- [ ] **Daily**:
- [ ] **Weekly**:
- [ ] **Monthly**:
- [ ] **Quarterly**:

## 🚀 Active Projects in Area
<%*
const projectQuery = `
TABLE WITHOUT ID
  file.link as "🚀 Project",
  choice(priority = "critical", "🔥", choice(priority = "high", "⚡", choice(priority = "medium", "📋", "💡"))) as "P",
  completion_percentage + "%" as "Progress",
  deadline as "Due"
FROM "1-Projects"
WHERE contains(related_areas, "${tp.file.title}")
  AND (status = "active" OR status = "on-hold")
SORT choice(priority = "critical", 1, choice(priority = "high", 2, 3)) ASC, deadline ASC
LIMIT 6
`;
tR += "```dataview\n" + projectQuery + "\n```\n\n";
%>

## 📚 Area Resources
<%*
const resourceQuery = `
LIST
FROM "3-Resources"
WHERE contains(related_areas, "${tp.file.title}")
   OR area_category = "${await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church"], ["Software-Development", "Administration", "Personal", "Church"], false, "Filter Area")}"
   OR contains(tags, "${await tp.system.suggester(["software-dev", "administration", "church", "personal"], ["software-dev", "administration", "church", "personal"], false, "Filter Tag")}")
SORT choice(contains(related_areas, "${tp.file.title}"), 1, 2) ASC, file.mtime DESC
LIMIT 5
`;
tR += "```dataview\n" + resourceQuery + "\n```\n\n";
%>

## 📅 Upcoming Area Reviews
<%*
const reviewQuery = `
TABLE WITHOUT ID
  file.link as "📅 Review Needed",
  next_review_date as "Due",
  responsibility_level as "Priority"
FROM "2-Areas"
WHERE next_review_date <= date(today) + dur(14 days)
  AND file.name != "${tp.file.title}"
  AND status = "active"
SORT next_review_date ASC
LIMIT 3
`;
tR += "```dataview\n" + reviewQuery + "\n```\n\n";
%>

## 📝 Recent Area Activity
<%*
const activityQuery = `
TABLE WITHOUT ID
  file.link as "📝 Recent Activity",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Templates"
WHERE (contains(related_areas, "${tp.file.title}")
   OR contains(file.content, "[[${tp.file.title}]]"))
  AND file.mtime >= date(today) - dur(7 days)
SORT file.mtime DESC
LIMIT 5
`;
tR += "```dataview\n" + activityQuery + "\n```\n\n";
%>

## Key Metrics
<!-- Metrics to track in this area -->
-

## Quick Links
- [[<% tp.file.title %> Project|New Project]]
- [[<% tp.file.title %> Resource|New Resource]]
- [[<% tp.file.title %> Meeting|New Meeting]]
- [[2-Areas|All Areas]]

---
creation_date: <% tp.date.now("YYYY-MM-DD HH:mm") %>
modification_date: <% tp.date.now("dddd Do MMMM YYYY HH:mm:ss") %>
type: daily
date: <% tp.date.now("YYYY-MM-DD") %>
day_of_week: <% tp.date.now("dddd") %>
week: <% tp.date.now("YYYY-[W]WW") %>
month: <% tp.date.now("YYYY-MM") %>
tags: [daily, <% tp.date.now("YYYY-MM") %>]
mood: ""
energy_level: ""
weather: ""
location: ""
related:
  depends-on: []
  blocks: []
  area-overlap: []
  references: []
  supports: []
  relates-to: []
---

# <% tp.date.now("YYYY-MM-DD") %> - <% tp.date.now("dddd") %>

<< [[<% tp.date.now("YYYY-MM-DD", -1) %>]] | [[<% tp.date.now("YYYY-MM-DD", 1) %>]] >>

> **🔄 Template not processed?** Click here: [Process Template](obsidian://advanced-uri?vault=cruca-docs&commandid=templater-obsidian%3Areplace-in-file-templater)

## Quick Capture
<!-- Use this section for quick thoughts that you'll organize later -->
- 

## 🚨 Critical Tasks (Priority Score 80+)
<%*
// Function to calculate task priority score
function calculateTaskPriority(task, fileDate, tags = [], area = "") {
  let score = 0;
  
  // Due date analysis from task text
  const today = new Date();
  const taskDate = new Date(fileDate);
  const diffTime = taskDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  // Due date weight
  if (diffDays < 0) score += 100; // Overdue
  else if (diffDays === 0) score += 80; // Today
  else if (diffDays <= 7) score += 60; // This week
  else if (diffDays <= 30) score += 40; // This month
  else score += 20; // Later
  
  // Priority indicators in task text
  if (task.toLowerCase().includes("urgent") || task.toLowerCase().includes("asap")) score += 20;
  if (task.toLowerCase().includes("important") || task.toLowerCase().includes("priority")) score += 15;
  if (task.toLowerCase().includes("critical") || task.toLowerCase().includes("emergency")) score += 25;
  
  // Tag weights
  const tagWeights = {
    "critical": 25, "urgent": 20, "important": 15, "church": 15,
    "admin": 10, "personal": 5, "finance": 20, "compliance": 25
  };
  
  tags.forEach(tag => {
    if (tagWeights[tag]) score += tagWeights[tag];
  });
  
  // Area importance
  const areaWeights = {
    "Church": 15, "Administration": 10, "Software-Development": 12,
    "Finance": 20, "Compliance": 25
  };
  
  if (areaWeights[area]) score += areaWeights[area];
  
  return score;
}

// Function to get prioritized unfinished tasks
async function getPrioritizedUnfinishedTasks() {
  const today = tp.date.now("YYYY-MM-DD");
  const dailyNotesFolder = "0-Daily Notes";
  let allTasks = [];

  try {
    const folder = app.vault.getAbstractFileByPath(dailyNotesFolder);
    
    if (folder && folder.children) {
      for (const file of folder.children) {
        if (!file.name.endsWith('.md') || file.name === `${today}.md` || file.name === 'Daily Notes TOC.md') {
          continue;
        }

        const dateMatch = file.name.match(/^(\d{4}-\d{2}-\d{2})\.md$/);
        if (!dateMatch) continue;

        const fileDate = dateMatch[1];

        try {
          const content = await app.vault.read(file);
          const tasks = content.match(/^[\s]*[-\*\+] \[ \] .+$/gm);
          
          // Extract tags from frontmatter
          const frontmatterMatch = content.match(/^---\n([\s\S]*?)\n---/);
          let tags = [];
          let area = "";
          
          if (frontmatterMatch) {
            const frontmatter = frontmatterMatch[1];
            const tagsMatch = frontmatter.match(/tags:\s*\[(.*?)\]/s);
            if (tagsMatch) {
              tags = tagsMatch[1].split(',').map(t => t.trim().replace(/['"]/g, ''));
            }
            
            const areaMatch = frontmatter.match(/area:\s*(.+)/);
            if (areaMatch) {
              area = areaMatch[1].trim().replace(/['"]/g, '');
            }
          }

          if (tasks && tasks.length > 0) {
            tasks.forEach(task => {
              const priority = calculateTaskPriority(task, fileDate, tags, area);
              allTasks.push({
                task: task,
                date: fileDate,
                fileName: file.name,
                priority: priority,
                tags: tags,
                area: area
              });
            });
          }
        } catch (error) {
          continue;
        }
      }
    }
  } catch (error) {
    console.log("Could not access daily notes folder:", error);
  }

  // Sort by priority (highest first)
  allTasks.sort((a, b) => b.priority - a.priority);
  
  return allTasks;
}

// Get and categorize tasks
const prioritizedTasks = await getPrioritizedUnfinishedTasks();

// Critical tasks (80+)
const criticalTasks = prioritizedTasks.filter(t => t.priority >= 80);
const highTasks = prioritizedTasks.filter(t => t.priority >= 60 && t.priority < 80);
const standardTasks = prioritizedTasks.filter(t => t.priority >= 40 && t.priority < 60);
const lowTasks = prioritizedTasks.filter(t => t.priority < 40);

if (criticalTasks.length > 0) {
  criticalTasks.forEach(taskItem => {
    const daysDiff = Math.floor((new Date(tp.date.now("YYYY-MM-DD")) - new Date(taskItem.date)) / (1000 * 60 * 60 * 24));
    const daysAgo = daysDiff === 1 ? "yesterday" : daysDiff === 0 ? "today" : `${daysDiff} days ago`;
    tR += `**From ${taskItem.date} (${daysAgo}) - Priority: ${taskItem.priority}**\n`;
    tR += taskItem.task + "\n\n";
  });
} else {
  tR += "*No critical tasks - great job!*\n";
}
%>

## ⚡ High Priority Tasks (Score 60-79)
<%*
if (highTasks.length > 0) {
  tR += `<details><summary>📋 High Priority Tasks (${highTasks.length} items)</summary>\n\n`;
  highTasks.forEach(taskItem => {
    const daysDiff = Math.floor((new Date(tp.date.now("YYYY-MM-DD")) - new Date(taskItem.date)) / (1000 * 60 * 60 * 24));
    const daysAgo = daysDiff === 1 ? "yesterday" : daysDiff === 0 ? "today" : `${daysDiff} days ago`;
    tR += `**From ${taskItem.date} (${daysAgo}) - Priority: ${taskItem.priority}**\n`;
    tR += taskItem.task + "\n\n";
  });
  tR += "</details>\n\n";
} else {
  tR += "*No high priority tasks*\n\n";
}
%>

## 📋 Standard Tasks (Score 40-59)
<%*
if (standardTasks.length > 0) {
  tR += `<details><summary>📝 Standard Tasks (${standardTasks.length} items)</summary>\n\n`;
  standardTasks.slice(0, 10).forEach(taskItem => {  // Limit to 10 most recent
    const daysDiff = Math.floor((new Date(tp.date.now("YYYY-MM-DD")) - new Date(taskItem.date)) / (1000 * 60 * 60 * 24));
    const daysAgo = daysDiff === 1 ? "yesterday" : daysDiff === 0 ? "today" : `${daysDiff} days ago`;
    tR += `From ${taskItem.date} (${daysAgo}) - Priority: ${taskItem.priority}\n`;
    tR += taskItem.task + "\n\n";
  });
  if (standardTasks.length > 10) {
    tR += `*... and ${standardTasks.length - 10} more standard tasks*\n`;
  }
  tR += "</details>\n\n";
} else {
  tR += "*No standard tasks*\n\n";
}
%>

## 📝 Low Priority Tasks (Score <40)
<%*
if (lowTasks.length > 0) {
  tR += `<details><summary>🗂️ Low Priority Tasks (${lowTasks.length} items)</summary>\n\n`;
  lowTasks.slice(0, 5).forEach(taskItem => {  // Limit to 5 most recent
    const daysDiff = Math.floor((new Date(tp.date.now("YYYY-MM-DD")) - new Date(taskItem.date)) / (1000 * 60 * 60 * 24));
    const daysAgo = daysDiff === 1 ? "yesterday" : daysDiff === 0 ? "today" : `${daysDiff} days ago`;
    tR += `From ${taskItem.date} (${daysAgo}) - Priority: ${taskItem.priority}\n`;
    tR += taskItem.task + "\n\n";
  });
  if (lowTasks.length > 5) {
    tR += `*... and ${lowTasks.length - 5} more low priority tasks*\n`;
  }
  tR += "</details>\n\n";
} else {
  tR += "*No low priority tasks*\n\n";
}
%>

## ✨ New Tasks for Today
#todo #tasks #outstanding
- [ ] 
- [ ] 
- [ ] 

## Church Matters
<!-- Church-related activities, meetings, pastoral care -->
- 

## Hall Hire
<!-- Hall bookings, inquiries, maintenance -->
- 

## Administration
<!-- Administrative tasks, correspondence, documentation -->
- 

## Finance & Banking
<!-- Banking, donations, payments, reconciliation -->
- 

## Safe Church
<!-- Compliance, training, documentation -->
- 

## Follow-ups
<!-- Items requiring follow-up action -->
- [ ] 
- [ ] 

## Journal
<!-- How was your day? What happened? What did you learn? -->
- 

## Notes
<!-- Any other notes or information -->
- 

## 🎯 Today's Focus Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress",
  priority_score as "Score"
FROM "1-Projects"
WHERE (status = "active" OR !contains(status, "completed")) AND priority_score >= 60
SORT priority_score DESC, deadline ASC
LIMIT 5
```

## 📊 Areas Needing Attention
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  responsibility_level as "Responsibility",
  next_review_date as "Next Review",
  priority_score as "Score"
FROM "2-Areas"
WHERE priority_score >= 50 OR next_review_date <= date(<% tp.date.now("YYYY-MM-DD") %>)
SORT priority_score DESC, next_review_date ASC
LIMIT 5
```

## 🔗 Today's Context
<%*
// Smart context based on day of week and recurring patterns
const dayOfWeek = tp.date.now("dddd");
const contexts = {
  "Monday": ["weekly-planning", "admin", "church"],
  "Tuesday": ["development", "personal"],
  "Wednesday": ["meetings", "church"],
  "Thursday": ["admin", "finance"],
  "Friday": ["wrap-up", "planning"],
  "Saturday": ["personal", "family"],
  "Sunday": ["church", "reflection"]
};

const todaysContexts = contexts[dayOfWeek] || [];

if (todaysContexts.length > 0) {
  tR += `*${dayOfWeek} focus areas: ${todaysContexts.join(", ")}*\n\n`;
  
  // Show relevant resources for today's context
  todaysContexts.forEach(context => {
    tR += `### ${context.charAt(0).toUpperCase() + context.slice(1)} Resources\n`;
    tR += "```dataview
\n";
    tR += "TABLE WITHOUT ID\n";
    tR += "  file.link as \"Resource\",\n";
    tR += "  resource_type as \"Type\",\n";
    tR += "  usefulness_rating as \"Rating\"\n";
    tR += "FROM \"3-Resources\"\n";
    tR += "WHERE contains(tags, \"" + context + "\") AND usefulness_rating >= 4\n";
    tR += "SORT usefulness_rating DESC\n";
    tR += "LIMIT 3\n";
    tR += "
```\n\n";
  });
}
%>

## 📅 Today's Meetings
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
  file.link as "Meeting",
  time as "Time",
## 📅 Today's Meetings
```dataview
  });
}

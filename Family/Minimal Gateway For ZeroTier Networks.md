---
creation_date: 2025-04-06
modification_date: 2025-04-06
type: note
aliases:
  - Minimal ZeroTier Gateway
  - Secure Network Bridge
tags:
  - networking
  - zerotier
  - gateway
  - security
  - hardening
area: Infrastructure
project: Bridge Network
status: active
priority: high
links:
  - "[[Secure ZeroTier Network Separation]]"
  - "[[Bridge network to access zero tier services from internet]]"
related:
  - "[[EC2 Bridge Server Maintenance]]"
  - "[[Media Streaming Server]]"
---
# Minimal Gateway For ZeroTier Networks

This document outlines how to create a minimal, hardened gateway server that connects two ZeroTier networks without exposing sensitive services directly to potentially compromised systems.

## Gateway Design Principles

1. **Minimalism**: Only the absolutely necessary components
2. **Expendability**: Contains no sensitive data or services
3. **Limited Purpose**: Only forwards HTTP/HTTPS trafficb
4. **Hardened**: Reduced attack surface and security controls
5. **Isolated**: Acts purely as a network gateway

## Hardware Options

### 1. Lightweight Virtual Machine (Recommended)

- **Pros**: Easily backed up, restored, or recreated; isolated from other systems
- **Cons**: Requires virtualization on an existing system
- **Requirements**: 1 CPU core, 512MB RAM, 8GB storage

### 2. Raspberry Pi or Similar SBC

- **Pros**: Low cost, physically separate hardware
- **Cons**: More vulnerable to hardware failures
- **Requirements**: Any Raspberry Pi model (even older models are sufficient)

### 3. Repurposed Old Computer

- **Pros**: Likely already available, no additional cost
- **Cons**: Higher power consumption, possibly louder
- **Requirements**: Any x86 computer that can run Linux

## Software Setup

### Base Operating System

Use a minimal server Linux distribution:

1. **Alpine Linux** (Smallest footprint, ~130MB installed size)
2. **Ubuntu Server Minimal** (More familiar for many users)
3. **Debian Minimal** (Good balance of minimalism and ease of use)

### Installation Steps for Minimal Ubuntu Server

1. **Download Ubuntu Server**:
   - Get the [Ubuntu Server](https://ubuntu.com/download/server) image
   - During installation, select "Minimal installation"

2. **Install Only Essential Packages**:
   ```bash
   sudo apt update
   sudo apt install -y iptables iptables-persistent net-tools tcpdump zerotier-one
   ```

3. **Remove Unnecessary Services**:
   ```bash
   sudo apt purge -y snapd cloud-init landscape-common
   sudo systemctl disable apache2 2>/dev/null
   sudo systemctl disable nginx 2>/dev/null
   sudo systemctl disable bind9 2>/dev/null
   ```

4. **Join Both ZeroTier Networks**:
   ```bash
   sudo zerotier-cli join <bridge_network_id>  # Bridge network
   sudo zerotier-cli join <service_network_id>  # Services network
   ```

5. **Enable IP Forwarding**:
   ```bash
   sudo sysctl -w net.ipv4.ip_forward=1
   echo "net.ipv4.ip_forward=1" | sudo tee -a /etc/sysctl.conf
   ```

## Network Configuration

### 1. Identify Network Interfaces

After joining both ZeroTier networks, identify the interfaces:

```bash
ip a | grep zt
```

Example output:
```
10: ztabcd1234: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 2800...  # Bridge network interface
11: ztxyz56789: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 2800...  # Service network interface
```

Take note of both interface names and their respective IP addresses.

### 2. Set Up iptables Forwarding Rules

```bash
# Variables (replace with your actual values)
BRIDGE_IFACE="ztabcd1234"            # ZeroTier bridge network interface
SERVICE_IFACE="ztxyz56789"           # ZeroTier service network interface
NGINX_SERVER="*************"         # Your Nginx server IP on service network
BRIDGE_IP="*************"            # Gateway's IP on bridge network
SERVICE_IP="**************"          # Gateway's IP on service network

# Clear existing rules
sudo iptables -F
sudo iptables -t nat -F

# Set default policies
sudo iptables -P INPUT DROP
sudo iptables -P FORWARD DROP
sudo iptables -P OUTPUT ACCEPT

# Allow established connections and loopback
sudo iptables -A INPUT -m conntrack --ctstate ESTABLISHED,RELATED -j ACCEPT
sudo iptables -A INPUT -i lo -j ACCEPT

# Allow SSH only from local network (if needed)
sudo iptables -A INPUT -p tcp --dport 22 -s ***********/24 -j ACCEPT

# Allow ping (optional, for diagnostics)
sudo iptables -A INPUT -p icmp --icmp-type echo-request -j ACCEPT

# Allow ZeroTier
sudo iptables -A INPUT -i $BRIDGE_IFACE -j ACCEPT
sudo iptables -A INPUT -i $SERVICE_IFACE -j ACCEPT

# Forward HTTP/HTTPS from bridge network to service network
sudo iptables -t nat -A PREROUTING -i $BRIDGE_IFACE -p tcp --dport 80 -j DNAT --to-destination $NGINX_SERVER:80
sudo iptables -t nat -A PREROUTING -i $BRIDGE_IFACE -p tcp --dport 443 -j DNAT --to-destination $NGINX_SERVER:443

# Allow forwarding for HTTP/HTTPS
sudo iptables -A FORWARD -i $BRIDGE_IFACE -o $SERVICE_IFACE -p tcp -d $NGINX_SERVER --dport 80 -j ACCEPT
sudo iptables -A FORWARD -i $BRIDGE_IFACE -o $SERVICE_IFACE -p tcp -d $NGINX_SERVER --dport 443 -j ACCEPT

# Allow return traffic
sudo iptables -A FORWARD -i $SERVICE_IFACE -o $BRIDGE_IFACE -m state --state ESTABLISHED,RELATED -j ACCEPT

# Set up masquerading (NAT)
sudo iptables -t nat -A POSTROUTING -o $SERVICE_IFACE -j MASQUERADE

# Save rules
sudo netfilter-persistent save
```

### 3. Configure iptables to Auto-Start on Boot

```bash
sudo systemctl enable netfilter-persistent
```

## Additional Security Hardening

### 1. Disable Unused Services

```bash
# List all running services
sudo systemctl list-units --type=service --state=running

# Disable unnecessary services (examples)
sudo systemctl disable bluetooth.service cups.service avahi-daemon.service
```

### 2. Restrict SSH Access

Edit `/etc/ssh/sshd_config`:

```
PermitRootLogin no
PasswordAuthentication no
AllowUsers yourusername
```

Then restart SSH:

```bash
sudo systemctl restart sshd
```

### 3. Set Up Automatic Security Updates

For Ubuntu/Debian:

```bash
sudo apt install -y unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades
```

### 4. Install Fail2ban to Protect SSH

```bash
sudo apt install -y fail2ban
sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

### 5. Configure a Basic Firewall (Optional Additional Layer)

```bash
sudo apt install -y ufw
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow from ***********/24 to any port 22 proto tcp
sudo ufw enable
```

## Monitoring and Maintenance

### Set Up Simple Monitoring

```bash
# Install monitoring tools
sudo apt install -y htop iftop vnstat

# Start and enable vnstat for traffic monitoring
sudo systemctl enable vnstat
sudo systemctl start vnstat
```

### Create a Traffic Logging Script

Create a file `/usr/local/bin/log-traffic.sh`:

```bash
#!/bin/bash
LOG_DIR="/var/log/gateway"
mkdir -p $LOG_DIR
DATE=$(date +"%Y-%m-%d")
HOUR=$(date +"%H")
LOG_FILE="$LOG_DIR/traffic-$DATE-$HOUR.log"

# Log current connections
echo "======= Connection Log $(date) =======" >> $LOG_FILE
ss -tn >> $LOG_FILE
echo "" >> $LOG_FILE

# Log interface statistics
echo "======= Interface Statistics =======" >> $LOG_FILE
ifconfig ztabcd1234 >> $LOG_FILE
ifconfig ztxyz56789 >> $LOG_FILE
echo "" >> $LOG_FILE

# Log NAT table
echo "======= NAT Table =======" >> $LOG_FILE
iptables -t nat -L -v -n >> $LOG_FILE
echo "" >> $LOG_FILE

# Trim logs (keep only last 7 days)
find $LOG_DIR -type f -name "traffic-*" -mtime +7 -delete
```

Make it executable and add to crontab:

```bash
sudo chmod +x /usr/local/bin/log-traffic.sh
echo "0 * * * * root /usr/local/bin/log-traffic.sh" | sudo tee -a /etc/cron.d/traffic-logging
```

## Disaster Recovery

### Create a Backup Script

Create a file `/usr/local/bin/backup-gateway.sh`:

```bash
#!/bin/bash
BACKUP_DIR="/var/backups/gateway"
mkdir -p $BACKUP_DIR
DATE=$(date +"%Y-%m-%d")

# Backup iptables rules
iptables-save > $BACKUP_DIR/iptables-$DATE.rules

# Backup essential configs
tar -czf $BACKUP_DIR/configs-$DATE.tar.gz /etc/network /etc/zerotier-one /etc/netplan

# Keep only the last 5 backups
find $BACKUP_DIR -name "iptables-*.rules" -type f -printf '%T@ %p\n' | sort -n | head -n -5 | cut -d' ' -f2- | xargs rm -f
find $BACKUP_DIR -name "configs-*.tar.gz" -type f -printf '%T@ %p\n' | sort -n | head -n -5 | cut -d' ' -f2- | xargs rm -f
```

Make it executable and add to crontab:

```bash
sudo chmod +x /usr/local/bin/backup-gateway.sh
echo "0 2 * * * root /usr/local/bin/backup-gateway.sh" | sudo tee -a /etc/cron.d/gateway-backup
```

### Document Restore Procedure

Keep a copy of these restore instructions in a secure location:

1. **Reinstall OS**: Install minimal Ubuntu Server
2. **Install Required Packages**: 
   ```bash
   sudo apt update && sudo apt install -y iptables iptables-persistent net-tools zerotier-one
   ```
3. **Restore ZeroTier Configuration**:
   ```bash
   sudo systemctl stop zerotier-one
   sudo tar -xzf configs-*.tar.gz -C / etc/zerotier-one
   sudo systemctl start zerotier-one
   sudo zerotier-cli join <bridge_network_id>
   sudo zerotier-cli join <service_network_id>
   ```
4. **Restore iptables Rules**:
   ```bash
   sudo iptables-restore < iptables-*.rules
   sudo netfilter-persistent save
   ```

## EC2 Bridge Server Configuration

Update your EC2 bridge server to forward traffic to your new gateway:

```bash
# Variables
GATEWAY_IP="*************"  # IP of the gateway on bridge network

# Forward HTTP/HTTPS to gateway
sudo iptables -t nat -A PREROUTING -i enX0 -p tcp --dport 80 -j DNAT --to-destination $GATEWAY_IP:80
sudo iptables -t nat -A PREROUTING -i enX0 -p tcp --dport 443 -j DNAT --to-destination $GATEWAY_IP:443

# Allow forwarding
sudo iptables -A FORWARD -i enX0 -o ztmjfcpubr -p tcp --dport 80 -j ACCEPT
sudo iptables -A FORWARD -i enX0 -o ztmjfcpubr -p tcp --dport 443 -j ACCEPT
sudo iptables -A FORWARD -i ztmjfcpubr -o enX0 -m state --state ESTABLISHED,RELATED -j ACCEPT

# Set up masquerading
sudo iptables -t nat -A POSTROUTING -o ztmjfcpubr -j MASQUERADE

# Save rules
sudo netfilter-persistent save
```

## ZeroTier Flow Rules

You'll still need flow rules on both ZeroTier networks:

### Bridge Network Rules

```
# Default: Drop non-IP traffic
drop
  not ethertype ipv4
  and not ethertype arp
  and not ethertype ipv6
;

# Allow HTTP/HTTPS to gateway from bridge
accept
  ipprotocol tcp
  and dport 80
  and ipdest *************/32  # Gateway's IP on bridge network
;

accept
  ipprotocol tcp
  and dport 443
  and ipdest *************/32  # Gateway's IP on bridge network
;

# Allow return traffic from gateway to bridge
accept
  ipprotocol tcp
  and sport 80
  and ipsrc *************/32  # Gateway's IP
  and ipdest ************/32  # Bridge's IP
;

accept
  ipprotocol tcp
  and sport 443
  and ipsrc *************/32  # Gateway's IP
  and ipdest ************/32  # Bridge's IP
;

# Allow ICMP and ARP
accept
  icmp 0 -1
;

accept
  icmp 8 -1
;

accept
  ethertype arp
;

# Drop everything else
drop;
```

### Services Network Rules

```
# Default: Drop non-IP traffic
drop
  not ethertype ipv4
  and not ethertype arp
  and not ethertype ipv6
;

# Allow HTTP/HTTPS from gateway to Nginx
accept
  ipprotocol tcp
  and dport 80
  and ipdest *************/32  # Nginx server IP
  and ipsrc **************/32  # Gateway's IP on service network
;

accept
  ipprotocol tcp
  and dport 443
  and ipdest *************/32  # Nginx server IP
  and ipsrc **************/32  # Gateway's IP on service network
;

# Allow return traffic
accept
  ipprotocol tcp
  and sport 80
  and ipsrc *************/32  # Nginx server IP
  and ipdest **************/32  # Gateway's IP
;

accept
  ipprotocol tcp
  and sport 443
  and ipsrc *************/32  # Nginx server IP
  and ipdest **************/32  # Gateway's IP
;

# Allow internal communication (adjust as needed)
accept
  ipsrc **********/16
  and ipdest **********/16
  and not ipsrc **************/32  # Exclude gateway from this rule
;

# Allow ICMP and ARP
accept
  icmp 0 -1
;

accept
  icmp 8 -1
;

accept
  ethertype arp
;

# Drop everything else
drop;
```

## Testing the Gateway

### 1. Verify ZeroTier Connectivity

```bash
# On gateway
sudo zerotier-cli listnetworks
```

Ensure both networks show "OK" status.

### 2. Test Forwarding

From the bridge server:

```bash
curl -v http://*************
```

This should reach your Nginx server through the gateway.

### 3. Test End-to-End

Access your domain in a web browser, which should:
1. Reach your EC2 bridge server
2. Forward to your gateway
3. Forward to your Nginx server
4. Return the response through the same path

## Common Issues and Solutions

### 1. Traffic Not Forwarding

**Symptoms**: Requests don't reach Nginx

**Solutions**:
- Verify IP forwarding is enabled: `cat /proc/sys/net/ipv4/ip_forward`
- Check both ZeroTier networks are connected: `sudo zerotier-cli listnetworks`
- Verify iptables rules: `sudo iptables -L -v -n`
- Check NAT table: `sudo iptables -t nat -L -v -n`
- Ensure both ZeroTier networks have the gateway authorized

### 2. Return Traffic Issues

**Symptoms**: Requests reach Nginx but no response returns

**Solutions**:
- Check FORWARD chain for return traffic rules
- Verify masquerading is set up correctly
- Ensure both ZeroTier flow rules allow return traffic

### 3. Intermittent Connectivity

**Symptoms**: Connection works sometimes but fails other times

**Solutions**:
- Check for network congestion: `iftop`
- Verify system resources aren't exhausted: `htop`
- Check ZeroTier connection stability
- Consider increasing connection tracking limits:
  ```bash
  echo "net.netfilter.nf_conntrack_max=65536" | sudo tee -a /etc/sysctl.conf
  sudo sysctl -p
  ```

## Related Documentation

- [[Secure ZeroTier Network Separation]] - Detailed explanation of network separation
- [[Bridge network to access zero tier services from internet]] - Overview of network architecture
- [[EC2 Bridge Server Maintenance]] - EC2 bridge server configuration

## Tasks

- [ ] Identify a system to use for the gateway
- [ ] Install minimal OS
- [ ] Configure network interfaces
- [ ] Set up forwarding rules
- [ ] Configure monitoring
- [ ] Test end-to-end connectivity
- [ ] Document specific IPs and configurations 
---

# YendorCats Vault Integration Guide

## Overview

This guide covers the complete integration of HashiCorp Vault with your YendorCats cat breeder website project for secure management of Backblaze B2 credentials and other sensitive data.

## Tags
#yendorcats #vault #b2 #backblaze #integration #secrets #security #development

---

## Quick Start

### 🚀 **One-Command Setup**

Run this single command to set up everything:

```bash
./scripts/quick-vault-setup.sh
```

This script will:
- ✅ Install HashiCorp Vault (if needed)
- ✅ Configure and start Vault server
- ✅ Initialize and unseal Vault
- ✅ Prompt for your B2 credentials
- ✅ Store all secrets securely
- ✅ Update your application configuration
- ✅ Create management scripts

### 🧪 **Test Integration**

After setup, test everything works:

```bash
./scripts/test-vault-integration.sh
```

---

## What Gets Stored in Vault

Your Vault instance will securely store:

### **Application Secrets**
```json
{
  "DbConnectionString": "MySQL connection string",
  "JwtSecret": "Secure JWT signing key (auto-generated)",
  "JwtIssuer": "YendorCatsApi",
  "JwtAudience": "YendorCatsClients", 
  "JwtExpiryMinutes": 60,
  "RefreshExpiryDays": 7,
  "S3AccessKey": "Your Backblaze B2 Key ID",
  "S3SecretKey": "Your Backblaze B2 Application Key",
  "S3SessionToken": "",
  "ApiKey": "Additional API key (auto-generated)"
}
```

### **Storage Location**
- **Path**: `secret/yendorcats/app-secrets`
- **Engine**: KV v2 (versioned secrets)
- **Access**: Token-based authentication

---

## Application Configuration

### **Development Configuration**

Your `appsettings.Development.json` is automatically updated:

```json
{
  "Vault": {
    "Address": "http://localhost:8200",
    "Token": "hvs.your-application-token-here",
    "SecretPath": "secret/yendorcats/app-secrets"
  },
  "AWS": {
    "Region": "us-west-004",
    "UseCredentialsFromSecrets": true,
    "S3": {
      "BucketName": "yendorcats-images-dev",
      "UseDirectS3Urls": true,
      "ServiceUrl": "https://s3.us-west-004.backblazeb2.com",
      "PublicUrl": "https://f004.backblazeb2.com/file/yendorcats-images-dev/{key}",
      "UseCdn": false
    }
  }
}
```

### **How It Works**

1. **Development Mode**: Application reads from Vault
2. **Fallback**: If Vault unavailable, uses local config
3. **Production**: Always uses Vault (no fallback)

---

## Daily Operations

### **Starting Your Development Environment**

```bash
# 1. Start Vault (if not running)
~/.vault-start.sh

# 2. Start your application
cd backend/YendorCats.API
dotnet run
```

### **Stopping Vault**

```bash
~/.vault-stop.sh
```

### **Checking Vault Status**

```bash
~/.vault-status.sh
```

### **Viewing Your Secrets**

```bash
export VAULT_ADDR='http://127.0.0.1:8200'
vault kv get secret/yendorcats/app-secrets
```

---

## Management Scripts

The setup creates these management scripts in your home directory:

### **~/.vault-start.sh**
- Starts Vault server in background
- Creates PID file for management
- Shows UI URL

### **~/.vault-stop.sh**
- Stops Vault server gracefully
- Cleans up PID file

### **~/.vault-status.sh**
- Shows Vault server status
- Displays your stored secrets

---

## Security Features

### **Encryption**
- ✅ **At Rest**: All data encrypted in storage
- ✅ **In Transit**: HTTPS communication (production)
- ✅ **In Memory**: Secrets cleared after use

### **Access Control**
- ✅ **Token-based**: Application uses dedicated token
- ✅ **Time-limited**: Tokens expire (8760 hours = 1 year)
- ✅ **Renewable**: Tokens can be renewed before expiry

### **Audit Trail**
- ✅ **All Access Logged**: Every secret access recorded
- ✅ **Version History**: Previous secret versions retained
- ✅ **Change Tracking**: Who changed what and when

---

## Troubleshooting

### **Common Issues**

#### 1. "Connection refused (localhost:8200)"
```bash
# Start Vault
~/.vault-start.sh

# Check if running
vault status
```

#### 2. "Vault is sealed"
```bash
# Unseal Vault
head -3 ~/.vault-unseal-keys.txt | while read key; do
    vault operator unseal "$key"
done
```

#### 3. "Permission denied"
```bash
# Check token
vault token lookup

# Renew if needed
vault token renew
```

#### 4. "Secrets not found"
```bash
# Re-run setup
./scripts/quick-vault-setup.sh
```

### **Log Files**
- **Vault Server**: `~/.vault-server.log`
- **Application**: Check console output
- **Vault Audit**: Enable in production

---

## Production Considerations

### **Security Hardening**
- [ ] **Enable TLS**: Use HTTPS certificates
- [ ] **AppRole Auth**: Replace token auth with AppRole
- [ ] **Audit Logging**: Enable comprehensive audit logs
- [ ] **Backup Strategy**: Regular encrypted backups
- [ ] **Network Security**: Firewall and VPN access

### **High Availability**
- [ ] **Clustering**: Multi-node Vault cluster
- [ ] **Load Balancing**: Distribute requests
- [ ] **Monitoring**: Health checks and alerting
- [ ] **Disaster Recovery**: Backup and restore procedures

### **Compliance**
- [ ] **Access Policies**: Least-privilege access
- [ ] **Key Rotation**: Regular secret rotation
- [ ] **Audit Reviews**: Regular access audits
- [ ] **Documentation**: Maintain security procedures

---

## Backblaze B2 Integration

### **How B2 Credentials Are Used**

1. **Storage**: Vault stores your B2 Key ID and Application Key
2. **Retrieval**: Application fetches credentials at startup
3. **S3 Client**: AWS SDK uses credentials for B2 API calls
4. **Caching**: Credentials cached in memory for performance

### **B2 Bucket Configuration**

Your application is configured for:
- **Development Bucket**: `yendorcats-images-dev`
- **Production Bucket**: `yendorcats-images`
- **Region**: `us-west-004`
- **Direct URLs**: Enabled for better performance

### **Testing B2 Integration**

```bash
# Check stored credentials
vault kv get -field=S3AccessKey secret/yendorcats/app-secrets
vault kv get -field=S3SecretKey secret/yendorcats/app-secrets

# Test application
cd backend/YendorCats.API
dotnet run

# Check logs for S3 initialization
```

---

## Cost Benefits

### **Eliminated Costs**
- ❌ **AWS Secrets Manager**: $0.40/secret/month
- ❌ **AWS API Calls**: $0.05/10,000 requests
- ❌ **AWS Data Transfer**: Various charges

### **New Costs**
- ✅ **HashiCorp Vault**: Free (Community Edition)
- ✅ **Infrastructure**: Your existing server
- ✅ **Maintenance**: Minimal operational overhead

### **Annual Savings**
- **Per Secret**: ~$5/year
- **Total Project**: $25-50/year
- **Multi-Client**: $100-500/year savings

---

## Next Steps

### **Immediate (Today)**
1. ✅ Run `./scripts/quick-vault-setup.sh`
2. ✅ Test with `./scripts/test-vault-integration.sh`
3. ✅ Start your application: `dotnet run`
4. ✅ Upload a test image to verify B2 integration

### **This Week**
1. [ ] Set up automated Vault backups
2. [ ] Configure production Vault instance
3. [ ] Test secret rotation procedures
4. [ ] Document team access procedures

### **This Month**
1. [ ] Implement AppRole authentication
2. [ ] Set up monitoring and alerting
3. [ ] Create disaster recovery plan
4. [ ] Train team on Vault operations

---

## Support Resources

### **Documentation**
- **HashiCorp Vault**: https://www.vaultproject.io/docs
- **VaultSharp**: https://github.com/rajanadar/VaultSharp
- **Backblaze B2**: https://www.backblaze.com/b2/docs/

### **Community**
- **HashiCorp Community**: https://discuss.hashicorp.com/
- **Stack Overflow**: Tag `hashicorp-vault`
- **GitHub Issues**: VaultSharp repository

### **Emergency Contacts**
- **Vault Admin**: [Your contact info]
- **B2 Account**: [Backblaze support]
- **Infrastructure**: [Your hosting provider]

---

## Success Metrics

### **Security**
- [ ] Zero hardcoded credentials in code
- [ ] All secrets stored in Vault
- [ ] Regular secret rotation implemented
- [ ] Audit logging enabled

### **Reliability**
- [ ] 99.9% Vault uptime
- [ ] Successful daily backups
- [ ] Tested disaster recovery
- [ ] Monitoring and alerting active

### **Cost Efficiency**
- [ ] AWS Secrets Manager costs eliminated
- [ ] No additional infrastructure costs
- [ ] Reduced operational overhead
- [ ] Scalable for multiple clients

---

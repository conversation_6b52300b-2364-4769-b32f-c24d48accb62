---
creation_date: 2025-04-16
modification_date: 2025-04-21
type: resource
source: Stack Overflow
tags: [para/resources, cheatsheet, git, software-dev, programming]
area: Software-Development
difficulty: medium
url: https://stackoverflow.com/questions/tagged/git
---

# Git Command Cheatsheet

## Overview
A collection of useful Git commands for various scenarios.

## Find all repositories on the machine

```bash
alias git repositories='find / -name ".git"'
```
**Output report to a file (~/logs/git/):**
```bash
bmkdir $HOME/logs/git && find / -name ".git" >&1 | tee $HOME/logs/git/$HOST_git_repos-$(date +"%Y-%m-%d_%H-%M-%S).log")
```
*Consider adding the following pipes to shorten the output if needed:*
```bash
[command] | xargs -n 1 dirname | sort [args]'
```
## Repository History

### List All Files That Have Ever Existed in a Repository

**Method 1:**
```bash
git log --pretty=format: --name-status | cut -f2- | sort -u
```

**Method 2 (More Efficient):**
```bash
git log --pretty=format: --name-only --diff-filter=A | sort -u
```

## Common Git Commands

### Setup and Configuration
```bash
# Set username and email
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# Check configuration
git config --list
```

### Basic Commands
```bash
# Initialize a repository
git init

# Clone a repository
git clone <repository-url>

# Check status
git status

# Add files to staging
git add <file>
git add .  # Add all files

# Commit changes
git commit -m "Commit message"

# Push changes
git push origin <branch-name>

# Pull changes
git pull origin <branch-name>
```

### Branch Management
```bash
# List branches
git branch

# Create a new branch
git branch <branch-name>

# Switch to a branch
git checkout <branch-name>

# Create and switch to a new branch
git checkout -b <branch-name>

# Merge a branch
git merge <branch-name>

# Delete a branch
git branch -d <branch-name>
```

### History and Differences
```bash
# View commit history
git log

# View compact commit history
git log --oneline

# View changes in a file
git diff <file>

# View staged changes
git diff --staged
```

### Undoing Changes
```bash
# Discard changes in working directory
git checkout -- <file>

# Unstage a file
git reset HEAD <file>

# Amend the last commit
git commit --amend

# Reset to a specific commit
git reset --hard <commit-hash>
```

## Related
- [[3-Resources]]
- [[Software Development Resources]]

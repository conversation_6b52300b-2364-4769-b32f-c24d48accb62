Project Summary:

<PROJECT_SUMMARY>

Technical Requirements:
- MongoDB for database.
- GraphQL API for the frontend.
- React frontend.

Documentation and Comments:
- Write meaningful comments and documentation only when necessary.
- Don't use personal pronouns like "I" or "we" in comments or documentation.
- Write documentation for all functions, classes, and modules.
- Write meaningful docstrings that describe the intention and behavior of the function, class, or module, and explain assumptions.
- Keep docstrings up to date and to the point.

Error Handling:
- Don't wrap code in try except blocks unless you're catching a specific exception.

Printing and Logging:
- Use a logger for all logging needs.

Dependencies:
- Init all dependencies in the dependencies.py file.
- Pass dependencies to classes when they are initialized.

Configuration:
- Write the configuration in the config.py file.

Naming Conventions:
- Start private class variables with an underscore.
- Use UPPERER_SNAKE_CASE for constants.
- Always write MongoDB and not Mongo.

Execution Flow:
- When writing code, always write the tests first.
- Always run the tests to make sure the code works.

Clean Code:
- Write clean, readable, and maintainable code.
- Keep functions small and focused.
- Keep variables and functions names descriptive.
- Keep comments and documentation meaningful.

Development Flow:
- Always write the tests first.
- Always run the tests to make sure the code works.
- When given a task, write code and run it until it works well.

File Structure:
- Leave the **init**.py files empty.

Code Style:
- Always use single quotes for strings.

Rewrite, Improve, and Refactor:
- When refactoring or fixing code, make sure to keep the code clean and easy to understand without duplicating code.
- Keep the code clean and easy to understand.
- Keep the code DRY (Don't Repeat Yourself).
- Keep the code clean and easy to understand.
- After fixing an issue, mention the case in the docstring so future changes won't break it again.

Tests:
- Always write the tests first.
- Always run the tests to make sure the code works.
- Always keep the tests clean and up to date.
- Always run the tests in the venv.

Debugging:
- If you are not sure what the solution is, add debug prints to the code and run the tests.
- After fixing an issue remove the debug prints.

Async:
- Always use async unless the function is a simple function that doesn't need to be async.

Memory:
- When you need to remember something, update the .cursormemory.md file.
- Refer to the .cursormemory.md file to view the memory.
- Update the memory with project specifications, requirements, flow of the code, and other relevant information.
- Add instructions for development like how to add new GraphQL mutations and queries to the memory.

Planning:
- Always plan the code before writing it.
- Think about how the new code will fit into the existing codebase.
- Think about how the new code will interact with the other parts of the codebase.
- Think about how the new code will handle errors and edge cases.
- Think about how the new code will be used by the frontend.
- Think about how the new code will be used by the users.
- Think about how the new code will be used by the developers.
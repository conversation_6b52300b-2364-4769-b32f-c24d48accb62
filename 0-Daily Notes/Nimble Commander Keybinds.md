---
creation_date: 2025-06-10
modification_date: 2025-06-10
type: resource
source: Documentation
tags: [para/resources, reference]
area: Software-Development
difficulty: easy
url: 
---


- [ ] TODO: needs to be formatted appropriately #toformat
# Nimble Commander Keybinds

Action Key Equivalent

Nimble Commander Menu

About

Preferences Cmd + ,

Enable Admin Mode

Hide Nimble Commander Cmd + H

Hide Others Opt + Cmd+H

Show All

Quit Nimble Commander Cmd + Q

Quit and Close All Windows Opt + Cmd + Q

File Menu

New Window Cmd + N

New Folder Shift + Cmd + N

New Folder with Selection Ctrl + Cmd + N

New File Opt + Cmd + N

New Tab Cmd + T

Enter Return

Open Shift + Return

Reveal in Opposite Panel Opt + Return

Reveal in Opposite Panel Tab Opt + Cmd + Return

Paste Filename to Terminal Ctrl + Opt + Return

Paste Filenames to Terminal… Ctrl + Opt + Cmd + Return

Calculate Folders Sizes Shift + Opt + Return

Calculate All Folders Sizes Ctrl + Shift + Return

Duplicate Cmd + D

Add to Favorites Cmd + B

30Action Key Equivalent

Close Cmd + W

Close Window Close Other Tabs Find… Cmd + F

Find with Spotlight… Shift + Cmd + W

Opt + Cmd + W

Opt + Cmd + F

Find Next Cmd + G

Edit Menu

Copy Cmd + C

Paste Cmd + V

Move Item Here Select All Cmd + A

Deselect All Invert Selection Opt + Cmd + V

Opt + Cmd + A

Ctrl + Cmd + A

View Menu

Toggle Single-Pane Mode Swap Panels Cmd + U

Sync Panels Shift + Cmd + P

Opt + Cmd + U

View Mode Submenu

Toggle Short View Mode Toggle Medium View Mode Toggle Full View Mode Toggle Wide View Mode Toggle View Mode V Toggle View Mode VI Toggle View Mode VII Toggle View Mode VIII Toggle View Mode IX Toggle View Mode X Ctrl + 1

Ctrl + 2

Ctrl + 3

Ctrl + 4

Ctrl + 5

Ctrl + 6

Ctrl + 7

Ctrl + 8

Ctrl + 9

Ctrl + 0

Sorting Submenu

Sort By Name Sort By Extension Sort By Modified Time Sort By Size Sort By Creation Time Sort By Added Time Sort By Accessed Time Ctrl + Cmd + 1

Ctrl + Cmd + 2

Ctrl + Cmd + 3

Ctrl + Cmd + 4

Ctrl + Cmd + 5

Ctrl + Cmd + 6

Ctrl + Cmd + 7

Separate Folders From Files

Extensionless Folders

Natural Comparison

Case-Insensitive Comparison

Case-Sensitive Comparison

Show Hidden Files Shift + Cmd + .

Panels Position Submenu

Move Left Move Right Move Up Move Down Show Panels Focus Overlapped Terminal Show Tab Bar Show Toolbar Show Terminal Ctrl + Opt + Left

Ctrl + Opt + Right

Ctrl + Opt + Up

Ctrl + Opt + Down

Ctrl + Opt + O

Ctrl + Opt + Tab

Shift + Cmd + T

Opt + Cmd + T

Opt + Cmd + O

31Action Key Equivalent

Go Menu

Left Panel… F1

Right Panel… F2

Back Cmd + [

Forward Cmd + ]

Enclosing Folder Cmd + Up

Enter Cmd + Down

Follow Cmd + Right

Documents Shift + Cmd + O

Desktop Shift + Cmd + D

Downloads Opt + Cmd + L

Home Shift + Cmd + H

Library

Applications Utilities Shift + Cmd + A

Shift + Cmd + U

Root

Processes List Opt + Cmd + P

Favorites Submenu

Manage Favorites… Ctrl + Cmd + B

Recently Closed Submenu

Restore Last Closed Panel Shift + Cmd + R

Quick Lists Submenu

Parent Folders Cmd + 1

History Cmd + 2

Favorites Cmd + 3

Volumes Cmd + 4

Connections Cmd + 5

Tags Cmd + 6

Go To Folder… Shift + Cmd + G

Connect To Submenu

FTP Server…

SFTP Server…

WebDAV Server…

Dropbox Storage…

Network Share…

Manage Connections… Cmd + K

Command Menu

System Overview Cmd + L

Detailed Volume Information

File Attributes Ctrl + A

Open Extended Attributes Opt + Cmd + X

Copy Item Name Shift + Cmd + C

Copy Item Path Opt + Cmd + C

Copy Item Directory Shift + Opt + Cmd + C

Select With Mask Cmd + =

Select With Extension Opt + Cmd + =

Deselect With Mask Cmd + -

Deselect With Extension Opt + Cmd + -

Preview Cmd + Y

Internal Viewer Opt + F3

External Editor F4

Eject Volume Cmd + E

32Action Key Equivalent

Batch Rename… Ctrl + M

Copy To… F5

Copy As… Shift + F5

Move To… F6

Move As… Shift + F6

Rename In Place Ctrl + F6

Create Directory F7

Move To Trash Cmd + Backward Delete

Delete… F8

Delete Permanently… Shift + F8

Compress… F9

Compress To… Shift + F9

Links Submenu

Create Symbolic Link

Create Hard Link

Edit Symbolic Link

Window Menu

Minimize Cmd + M

Enter Full Screen Ctrl + Cmd + F

Zoom

Show Previous Tab Show Next Tab Shift + Ctrl + Tab

Ctrl + Tab

Show VFS List

Bring All To Front

Special Hotkeys

File Panels

Move Up Up

Move Down Down

Move Left Left

Move Right Right

Move to the First Element Home

Scroll to the First Element Opt + Home

Move to the Last Element End

Scroll to the Last Element Opt + End

Move to the Next Page Page Down

Scroll to the Next Page Opt + Page Down

Move to the Previous Page Page Up

Scroll to the Previous Page Opt + Page Up

Toggle Selection

Toggle Selection and Move Down Change Active panel Tab

Previous Tab Next Tab Go into Enclosing folder Enter

Shift + Cmd + [

Shift + Cmd + ]

Backward Delete

Go into Folder

Go to Home Folder Shift + ~

Go to Root Folder /

Show Preview Space

Show Tab №1

Show Tab №2

Show Tab №3

Show Tab №4

33Action Key Equivalent

Show Tab №5

Show Tab №6

Show Tab №7

Show Tab №8

Show Tab №9

Show Tab №10

Focus Left Panel Shift + Cmd + Left

Focus Right Panel Shift + Cmd + Right

Show Context Menu Ctrl + Return

Viewer

Toggle Text Cmd + 1

Toggle Hex Cmd + 2

Toggle Preview Cmd + 3

Show GoTo Cmd + L
Refresh Cmd + ?

## Related Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE contains(file.content, "[[Nimble Commander Keybinds]]")
SORT priority ASC
```

## Related Areas
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  area as "Category"
FROM "2-Areas"
WHERE contains(file.content, "[[Nimble Commander Keybinds]]") OR area = "Personal"
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "reference") AND file.name != "Nimble Commander Keybinds"
LIMIT 5
```

## Notes
<!-- Any additional notes -->

## Quick Links
- [[Nimble Commander Keybinds Project|New Project]]
- [[Nimble Commander Keybinds Reference|Quick Reference]]
- [[3-Resources|All Resources]]
R
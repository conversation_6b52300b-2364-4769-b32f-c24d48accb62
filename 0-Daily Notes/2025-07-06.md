---
creation_date: 2025-07-06 18:31
modification_date: Sunday 6th July 2025 18:31:37
type: daily
date: 2025-07-06
day_of_week: Sunday
week: 2025-W27
month: 2025-07
tags: [daily, 2025-07]
mood: ""
energy_level: ""
weather: ""
location: ""
---

# 2025-07-06 - Sunday

<< [[2025-07-05]] | [[2025-07-07]] >>

> **🔄 Template not processed?** Click here: [Process Template](obsidian://advanced-uri?vault=cruca-docs&commandid=templater-obsidian%3Areplace-in-file-templater)

## Quick Capture
<!-- Use this section for quick thoughts that you'll organize later -->
-

## Tasks for Today
#todo #tasks #outstanding
### ✨ Tasks for Today

- [ ]
- [ ]
- [ ]

## Church Matters
<!-- Church-related activities, meetings, pastoral care -->
-

## Hall Hire
<!-- Hall bookings, inquiries, maintenance -->
-

## Administration
<!-- Administrative tasks, correspondence, documentation -->
-

## Finance & Banking
<!-- Banking, donations, payments, reconciliation -->
-

## Safe Church
<!-- Compliance, training, documentation -->
-

## Follow-ups
<!-- Items requiring follow-up action -->
- [ ]
- [ ]

## Journal
<!-- How was your day? What happened? What did you learn? -->
-

## Notes
<!-- Any other notes or information -->
-

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE status = "active" OR !contains(status, "completed")
SORT priority ASC, deadline ASC
LIMIT 5
```

## Today's Meetings
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
WHERE date = date(2025-07-06)
SORT time ASC
```

## Today's Tasks Overview
```tasks
not done
due on 2025-07-06
```

## Tasks from This Note
```dataview
TASK
FROM "0-Daily Notes/2025-07-06.md"
WHERE !completed
```

## This Week's Tasks
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-07-06) AND due <= date(2025-07-12)
SORT due ASC
```

## This Month's Tasks
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-07-01) AND due <= date(2025-07-31)
SORT due ASC
```

## This Quarter's Tasks

```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-07-01) AND due <= date(2025-09-30)
SORT due ASC
```

## Overdue Tasks
```tasks
not done
due before 2025-07-06
```

## Upcoming Tasks (Next 7 Days)
```tasks
not done
due after 2025-07-06
due before 2025-07-13
```

## Church Administration Tasks
```dataview
TASK
FROM "2-Areas" OR "1-Projects"
WHERE !completed AND (contains(tags, "church") OR contains(tags, "admin") OR contains(tags, "hall-hire") OR contains(tags, "finance"))
SORT due ASC
LIMIT 10
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Daily" AND -"Templates"
SORT file.mtime DESC
LIMIT 5
```

## Create New Note
- [[2025-07-06 Meeting|Create New Meeting]]
- [[2025-07-06 Task|Create New Task]]
- [[2025-07-06 Idea|Create New Idea]]

## Related
- [[Daily Notes TOC]]
- [[2025-07|Monthly Overview]]
- [[2025-W27|Weekly Overview]]
- [[Tasks]]
- [[Home]]
